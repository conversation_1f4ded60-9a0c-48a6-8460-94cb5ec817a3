package com.olading.operate.labor.domain.invoice;

/**
 * 发票类型枚举
 */
public enum InvoiceType {
    SPECIAL("SPECIAL", "专用发票"),
    GENERAL("GENERAL", "普通发票");
    
    private final String code;
    private final String description;
    
    InvoiceType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() { 
        return code; 
    }
    
    public String getDescription() { 
        return description; 
    }
}