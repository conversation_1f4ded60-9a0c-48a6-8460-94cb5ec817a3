package com.olading.operate.labor.app.web.biz.boss;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.query.SupplierQuery;
import com.olading.operate.labor.domain.query.UserQuery;
import com.olading.operate.labor.domain.service.BossService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.service.SupplierService;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.PersonInfoData;
import com.olading.operate.labor.domain.share.user.UserData;
import com.olading.operate.labor.domain.share.user.UserManager;
import com.olading.operate.labor.domain.supplier.H5ServiceAgreement;
import com.olading.operate.labor.domain.supplier.SupplierData;
import com.olading.operate.labor.domain.supplier.SupplierPayChannelData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Tag(name = "BOSS管理接口")
@RestController
@RequestMapping("/api/boss")
@RequiredArgsConstructor
@Slf4j
public class BossController extends BusinessController {

    private final SupplierService supplierService;
    private final QueryService queryService;
    private final FileManager fileManager;
    private final BossService bossService;
    private final UserManager userManager;

    private static SupplierDetailVo toSupplierDetailVo(SupplierData data) {
        SupplierDetailVo vo = new SupplierDetailVo();
        vo.setId(data.getId());
        vo.setSupplierNo(data.getSupplierNo());
        vo.setBusinessCreateTime(data.getBusinessCreateTime());
        vo.setDisabled(data.isDisabled());
        vo.setContactPhone(data.getInfo().getContactPhone());
        vo.setContacts(data.getInfo().getContacts());
        vo.setName(data.getInfo().getName());
        vo.setSignatureCode(data.getSignatureCode());
        if(data.getDomain() != null){
            vo.setDomainName(data.getDomain().getDomainName());
            vo.setH5DomainName(data.getDomain().getH5DomainName());
            vo.setLogoUrl(data.getDomain().getLogoUrl());
            vo.setBrandName(data.getDomain().getBrandName());
            vo.setH5LogoUrl(data.getDomain().getH5LogoUrl());
            if(data.getDomain().getH5ServiceAgreement() != null)
            {
                vo.setH5ServiceAgreement(data.getDomain().getH5ServiceAgreement().stream().map(o -> {
                    return new H5ServiceAgreement(o.getName(),o.getUrl());
                }).collect(Collectors.toList()));
            }
        }
        vo.setAttachments(data.getInfo().getAttachments());
        return vo;
    }

    @Operation(summary = "获取BOSS后台登录用户的信息")
    @PostMapping(value = "profile")
    public WebApiResponse<SupplierProfileVo> profile() {

        SupplierProfileVo profile = new SupplierProfileVo();
        profile.setUserId(currentUserId());
        PersonInfoData info = userManager.getUserInfo(currentUserId());
        if (info != null) {
            profile.setUserPersonName(info.getName());
        }

        return WebApiResponse.success(profile);
    }

    @Operation(summary = "添加用户")
    @PostMapping(value = "addUser")
    public WebApiResponse<Void> addUser(@RequestBody UserVo request) {
        bossService.addUser(request.getCellphone(), request.getName());
        return WebApiResponse.success();
    }

    @Operation(summary = "修改用户")
    @PostMapping(value = "editUser")
    public WebApiResponse<Void> editUser(@RequestBody UserVo request) {
        bossService.editUser(request.getId(), request.getName());
        return WebApiResponse.success();
    }

    @Operation(summary = "删除用户")
    @PostMapping(value = "deleteUser")
    public WebApiResponse<Void> deleteUser(@RequestBody UserVo request) {
        bossService.deleteUser(request.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "查询用户列表")
    @PostMapping(value = "listUser")
    public WebApiQueryResponse<UserVo> listUser(@RequestBody QueryFilter<Void> request) {

        QueryFilter<UserQuery.Filters> filter = request.convert(o -> {
            UserQuery.Filters filters = new UserQuery.Filters();
            filters.setTenant(currentTenant(TenantInfo.TenantType.BOSS));
            return filters;
        });
        filter.sort("id", Direction.DESCENDING);
        DataSet<UserData> ds = queryService.queryUser(filter);
        return WebApiQueryResponse.success(ds.getData().stream()
                .map(o -> {
                    UserVo vo = new UserVo();
                    vo.setId(o.getId());
                    vo.setName(o.getInfo().getName());
                    vo.setCellphone(o.getCellphone());
                    return vo;
                })
                .collect(Collectors.toList()), ds.getTotal());
    }

    @Operation(summary = "添加服务商")
    @PostMapping(value = "addSupplier")
    public WebApiResponse<Void> addSupplier(@Valid @RequestBody SupplierDetailVo request) {
        SupplierData data = SupplierData.toSupplierData(request);
        data.setId(null);
        supplierService.addSupplier(data,currentTenant());
        return WebApiResponse.success();
    }

    @Operation(summary = "修改服务商")
    @PostMapping(value = "editSupplier")
    public WebApiResponse<Void> editSupplier(@Valid @RequestBody SupplierDetailVo request) {
        supplierService.editSupplier(SupplierData.toSupplierData(request));
        return WebApiResponse.success();
    }

    @Operation(summary = "启用停用服务商")
    @PostMapping(value = "setSupplierDisabled")
    public WebApiResponse<Void> setSupplierDisabled(@RequestBody SetSupplierDisabledRequest request) {
        supplierService.setSupplierDisabled(request.getSupplierId(), request.getDisabled());
        return WebApiResponse.success();
    }

    @Operation(summary = "查询服务商列表")
    @PostMapping(value = "listSupplier")
    public WebApiQueryResponse<SupplierDetailVo> listSupplier(@RequestBody QueryFilter<WebSupplierFilters> request) {

        QueryFilter<SupplierQuery.Filters> filter = request.convert(WebSupplierFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        DataSet<SupplierData> ds = queryService.querySupplier(filter);
        return WebApiQueryResponse.success(ds.getData().stream()
                .map(BossController::toSupplierDetailVo)
                .collect(Collectors.toList()), ds.getTotal());
    }

    @Operation(summary = "添加服务商可用支付通道")
    @PostMapping(value = "addPayChannel")
    public WebApiResponse<Void> addPayChannel(@Valid @RequestBody AddPayChannelRequest request) {
        final Set<SupplierPayChannelData> collect = request.getPayChannels().stream().map(payChannel -> {
            final SupplierPayChannelData supplierPayChannelData = new SupplierPayChannelData();
            supplierPayChannelData.setPayChannel(payChannel);
            supplierPayChannelData.setSupplierId(request.getId());
            return supplierPayChannelData;
        }).collect(Collectors.toSet());
        supplierService.addPayChannel(collect);
        return WebApiResponse.success();
    }





    @Data
    public static class SupplierProfileVo {
        @Schema(description = "用户ID")
        private Long userId;
        @Schema(description = "用户姓名")
        private String userPersonName;
    }

    @Data
    public static class SetSupplierDisabledRequest {
        @Schema(description = "服务商ID")
        private Long supplierId;
        @NotNull
        @Schema(description = "是否停用")
        private Boolean disabled;
    }

    @Data
    public static class UserVo {
        @Schema(description = "用户ID，添加时不需要传")
        private Long id;
        @Schema(description = "手机号")
        private String cellphone;
        @Schema(description = "用户姓名")
        private String name;
    }

    @Data
    public static class WebSupplierFilters {

        private String supplierNo;

        private String name;

        private LocalDateTime businessCreateTimeBegin;

        private LocalDateTime businessCreateTimeEnd;

        @Schema(description = "企业负责人")
        private String contacts;

        @Schema(description = "负责人联系方式")
        private String contactPhone;

        public SupplierQuery.Filters convert() {
            return Beans.copyBean(this, SupplierQuery.Filters.class);
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class SupplierDetailVo {
        private Long id;

        @Size(max = 100)
        @NotEmpty
        @Schema(description = "服务商编号")
        private String supplierNo;

        @Size(max = 100)
        @NotEmpty
        @Schema(description = "统一社会信用代码")
        private String socialCreditCode;

        @Size(max = 100)
        @Schema(description = "名称")
        private String name;

        @Size(max = 20)
        @Schema(description = "负责人")
        private String contacts;

        @Size(max = 11)
        @Schema(description = "负责人联系方式")
        private String contactPhone;

        @Size(max = 50)
        @NotEmpty
        @Schema(description = "服务商短信签名")
        private String signatureCode;


        @Size(max = 60)
        @NotEmpty
        @Schema(description = "二级域名")
        private String domainName;

        @Size(max = 20)
        @Schema(description = "slogan")
        private String slogan;

        @Size(max = 255)
        @Schema(description = "logo地址")
        @NotBlank(message = "logo地址不能为空")
        private String logoUrl;

        @Size(max = 255)
        @NotBlank
        @Schema(description = "品牌名称")
        private String brandName;

        @Size(max = 60)
        @Schema(description = "h5域名")
        private String h5DomainName;

        @Size(max = 255)
        @Schema(description = "h5_logo地址")
        @NotBlank(message = "h5_logo地址不能为空")
        private String h5LogoUrl;

        @Schema(description = "h5服务协议文件及文件地址")
        private List<H5ServiceAgreement> h5ServiceAgreement;

        @Schema(description = "创建时间")
        private LocalDateTime businessCreateTime;
        @Schema(description = "附件文件archiveId")
        private List<String> attachments;
        private boolean disabled;
    }


    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class AddPayChannelRequest {

        @Schema(description = "服务商Id")
        private Long id;

        @Schema(description = "通道编码")
        private List<String> payChannels;

    }


}
