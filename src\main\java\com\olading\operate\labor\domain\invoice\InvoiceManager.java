package com.olading.operate.labor.domain.invoice;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.bill.BillManager;
import com.olading.operate.labor.domain.bill.BillMasterEntity;
import com.olading.operate.labor.domain.bill.BillMasterStatus;
import com.olading.operate.labor.domain.bill.repository.BillMasterRepository;
import com.olading.operate.labor.domain.corporation.CorporationConfigEntity;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.invoice.dto.InvoiceCreateRequest;
import com.olading.operate.labor.domain.invoice.dto.InvoiceItemRequest;
import com.olading.operate.labor.domain.invoice.repository.InvoiceItemRepository;
import com.olading.operate.labor.domain.invoice.repository.InvoiceRepository;
import com.olading.operate.labor.domain.invoice.vo.AvailableBillVO;
import com.olading.operate.labor.domain.invoice.vo.InvoiceInfoVO;
import com.olading.operate.labor.domain.invoice.vo.InvoiceItemVO;
import com.olading.operate.labor.domain.invoice.vo.InvoicePresetVO;
import com.olading.operate.labor.domain.invoice.vo.InvoiceVO;
import com.olading.operate.labor.domain.share.contract.BusinessContractConfigEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractEntity;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.customer.CustomerEntity;
import com.olading.operate.labor.domain.share.customer.CustomerManager;
import com.olading.operate.labor.domain.share.customer.vo.CustomerVo;
import com.olading.operate.labor.domain.share.customer.vo.CustomerWithInfo;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 开票管理器
 */
@Component
@RequiredArgsConstructor
@Transactional
@Slf4j
public class InvoiceManager {

    private final EntityManager em;
    private final InvoiceRepository invoiceRepository;
    private final InvoiceItemRepository invoiceItemRepository;
    private final BillMasterRepository billMasterRepository;
    private final BillManager billManager;
    private final BusinessContractManager contractManager;
    private final CustomerManager customerManager;
    private final CorporationManager corporationManager;

    /**
     * 获取可开票账单列表
     */
    public List<AvailableBillVO> getAvailableBills(Long supplierId, Long contractId, LocalDate billMonth) {
        log.info("获取可开票账单列表: supplierId={}, contractId={}, billMonth={}",
                supplierId, contractId, billMonth);

        // 1. 查询合同下已确认的账单
        List<BillMasterEntity> confirmedBills = getConfirmedBillsByContract(contractId, billMonth);

        // 2. 获取作业主体的发票类目配置
        BusinessContractEntity contract = getContractInfo(contractId, supplierId);
        List<String> availableCategories = getInvoiceCategoriesByContract(contract);

        // 3. 计算每个账单的可开票金额
        List<AvailableBillVO> availableBills = new ArrayList<>();
        for (BillMasterEntity bill : confirmedBills) {
            BigDecimal availableAmount = bill.getTotalInvoiceAmount().subtract(bill.getInvoicedAmount());

            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                AvailableBillVO vo = convertToAvailableBillVO(bill, availableAmount, availableCategories);
                availableBills.add(vo);
            }
        }

        log.info("获取可开票账单完成: supplierId={}, contractId={}, 可开票账单数={}",
                supplierId, contractId, availableBills.size());

        return availableBills;
    }

    /**
     * 创建开票申请
     */
    @Transactional
    public InvoiceVO createInvoiceApplication(Long supplierId, InvoiceCreateRequest request) {
        log.info("创建开票申请: supplierId={}, contractId={}, totalFee={}",
                supplierId, request.getContractId(),
                request.getItems().stream().map(InvoiceItemRequest::getFee).reduce(BigDecimal.ZERO, BigDecimal::add));

        // 1. 验证请求参数
        validateInvoiceCreateRequest(request);

        // 2. 验证合同权限
        BusinessContractEntity contract = getContractInfo(request.getContractId(), supplierId);

        // 3. 验证账单状态和可开票金额
        validateBillsForInvoice(request.getItems());

        // 4. 获取合同开票配置
        BusinessContractConfigEntity contractConfig = getContractConfig(request.getContractId());

        // 5. 创建开票申请（状态为PENDING）
        InvoiceEntity invoice = createInvoiceEntity(supplierId, request, contract, contractConfig);
        invoice.setStatus(InvoiceStatus.PENDING);
        invoiceRepository.save(invoice);

        // 6. 创建开票明细
        List<InvoiceItemEntity> items = createInvoiceItems(invoice.getId(), request.getItems());
        invoiceItemRepository.saveAll(items);

        // 7. 更新账单已开票金额（预占用）
        updateBillInvoicedAmount(request.getItems());

        InvoiceVO result = convertToInvoiceVO(invoice, items);

        log.info("创建开票申请完成: supplierId={}, invoiceId={}, sn={}, totalFee={}",
                supplierId, invoice.getId(), invoice.getSn(), invoice.getFee());

        return result;
    }

    /**
     * 获取开票申请详情
     */
    public InvoiceVO getInvoiceDetail(Long supplierId, Long invoiceId) {
        log.info("获取开票申请详情: supplierId={}, invoiceId={}", supplierId, invoiceId);

        InvoiceEntity invoice = validateInvoicePermission(supplierId, invoiceId);
        List<InvoiceItemEntity> items = invoiceItemRepository.findByInvoiceIdAndDeletedFalse(invoiceId);

        InvoiceVO result = convertToInvoiceVO(invoice, items);

        // 设置关联信息
        setInvoiceRelatedInfo(result);

        return result;
    }

    /**
     * 更新开票状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInvoiceStatus(Long supplierId, Long invoiceId, InvoiceStatus newStatus, String reason, String invoiceFile) {
        log.info("更新开票状态: supplierId={}, invoiceId={}, newStatus={}, reason={}",
                supplierId, invoiceId, newStatus, reason);

        InvoiceEntity invoice = validateInvoicePermission(supplierId, invoiceId);

        // 验证状态流转的合法性
        validateStatusTransition(invoice.getStatus(), newStatus);

        InvoiceStatus oldStatus = invoice.getStatus();
        invoice.setStatus(newStatus);

        switch (newStatus) {
            case INVOICED:
                // 上传发票文件时
                if (StringUtils.hasText(invoiceFile)) {
                    invoice.setInvoiceFile(invoiceFile);
                }
                break;

            case RETURNED:
                // 退回时
                invoice.setBackReason(reason);
                // 释放已占用的开票金额
                releaseInvoicedAmount(invoiceId);
                break;
            default:
                throw new BusinessException("未知开票处理状态");
        }

        invoiceRepository.save(invoice);

        log.info("开票状态更新完成: invoiceId={}, {} -> {}, reason={}",
                invoiceId, oldStatus, newStatus, reason);
    }


    /**
     * 获取开票预设信息
     */
    public InvoicePresetVO getInvoicePresetInfo(Long supplierId, Long contractId, LocalDate billMonth) {
        log.info("获取开票预设信息: supplierId={}, contractId={}, billMonth={}",
                supplierId, contractId, billMonth);

        // 1. 获取合同信息并验证权限
        BusinessContractEntity contract = getContractInfo(contractId, supplierId);

        // 2. 获取合同配置
        BusinessContractConfigEntity contractConfig = getContractConfig(contractId);

        // 3. 获取可开票账单列表
        List<AvailableBillVO> availableBills = getAvailableBills(supplierId, contractId, billMonth);

        // 4. 获取发票类目配置
        List<String> availableCategories = getInvoiceCategoriesByContract(contract);

        // 5. 构建预设信息
        InvoicePresetVO preset = buildInvoicePresetVO(contract, contractConfig, availableCategories, availableBills);

        log.info("获取开票预设信息完成: supplierId={}, contractId={}, 可开票账单数={}",
                supplierId, contractId, availableBills.size());

        return preset;
    }

    /**
     * 验证开票申请权限
     */
    public InvoiceEntity validateInvoicePermission(Long supplierId, Long invoiceId) {
        InvoiceEntity invoice = invoiceRepository.findByIdAndSupplierIdAndDeletedFalse(invoiceId, supplierId)
                .orElseThrow(() -> new BusinessException("开票申请不存在或无权限访问"));
        return invoice;
    }

    // ==================== 私有方法 ====================

    /**
     * 获取合同下已确认的账单
     */
    private List<BillMasterEntity> getConfirmedBillsByContract(Long contractId, LocalDate billMonth) {
        return billManager.getConfirmedBillsByContract(contractId, billMonth);
    }

    /**
     * 获取合同信息并验证权限
     */
    private BusinessContractEntity getContractInfo(Long contractId, Long supplierId) {
        BusinessContractEntity contract = em.find(BusinessContractEntity.class, contractId);
        if (contract == null || contract.isDeleted()) {
            throw new BusinessException("合同不存在或已删除");
        }

        if (!contract.getSupplierId().equals(supplierId)) {
            throw new BusinessException("无权限访问该合同");
        }

        return contract;
    }

    /**
     * 获取合同配置
     */
    private BusinessContractConfigEntity getContractConfig(Long contractId) {
        BusinessContractConfigEntity config = contractManager.getConfigByContractId(contractId);
        if (config == null) {
            throw new BusinessException("合同配置不存在");
        }
        return config;
    }

    /**
     * 获取发票类目配置
     */
    private List<String> getInvoiceCategoriesByContract(BusinessContractEntity contract) {
        try {
            CorporationConfigEntity corporationConfig = corporationManager.getCorporationConfig(contract.getSupplierCorporationId());
            if (corporationConfig != null && StringUtils.hasText(corporationConfig.getInvoiceCategory())) {
                return Arrays.asList(corporationConfig.getInvoiceCategory().split(","));
            }
        } catch (Exception e) {
            log.warn("获取发票类目配置失败: corporationId={}", contract.getSupplierCorporationId(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 验证开票创建请求
     */
    private void validateInvoiceCreateRequest(InvoiceCreateRequest request) {
        if (request.getItems() == null || request.getItems().isEmpty()) {
            throw new BusinessException("开票明细不能为空");
        }

        // 验证开票金额总和
        BigDecimal totalFee = request.getItems().stream()
                .map(InvoiceItemRequest::getFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalFee.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("开票金额必须大于0");
        }
    }

    /**
     * 验证账单开票条件
     */
    private void validateBillsForInvoice(List<InvoiceItemRequest> items) {
        for (InvoiceItemRequest item : items) {
            BillMasterEntity bill = billMasterRepository.findById(item.getBillId())
                    .orElseThrow(() -> new BusinessException("账单不存在: " + item.getBillId()));

            // 验证账单状态
            if (bill.getBillStatus() != BillMasterStatus.CONFIRMED) {
                throw new BusinessException("只能对已确认的账单申请开票: " + bill.getBillNo());
            }

            // 验证可开票金额
            BigDecimal availableAmount = bill.getTotalInvoiceAmount().subtract(bill.getInvoicedAmount());
            if (item.getFee().compareTo(availableAmount) > 0) {
                throw new BusinessException("开票金额不能超过可开票金额: " + bill.getBillNo());
            }
        }
    }

    /**
     * 创建开票实体
     */
    private InvoiceEntity createInvoiceEntity(Long supplierId, InvoiceCreateRequest request,
                                            BusinessContractEntity contract, BusinessContractConfigEntity contractConfig) {
        InvoiceEntity invoice = new InvoiceEntity(TenantInfo.ofSupplier(supplierId));

        // 生成开票编号
        invoice.setSn(generateInvoiceSn());

        // 基本信息
        invoice.setSupplierId(supplierId);
        invoice.setCustomerId(request.getCustomerId());
        invoice.setSupplierCorporationId(contract.getSupplierCorporationId());
        invoice.setContractId(request.getContractId());
        invoice.setType(request.getType());

        // 计算总金额
        BigDecimal totalFee = request.getItems().stream()
                .map(InvoiceItemRequest::getFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        invoice.setFee(totalFee);

        // 发票信息（优先使用请求中的信息，其次使用合同配置）
        invoice.setTitle(request.getTitle());
        invoice.setTaxNo(request.getTaxNo());
        invoice.setBankName(StringUtils.hasText(request.getBankName()) ? request.getBankName() : contractConfig.getInvoiceBankName());
        invoice.setBankAccount(StringUtils.hasText(request.getBankAccount()) ? request.getBankAccount() : contractConfig.getInvoiceBankAccount());
        invoice.setRegisterAddress(StringUtils.hasText(request.getRegisterAddress()) ? request.getRegisterAddress() : contractConfig.getInvoiceRegisterAddress());
        invoice.setCompanyTel(StringUtils.hasText(request.getCompanyTel()) ? request.getCompanyTel() : contractConfig.getInvoiceCompanyTel());
        invoice.setRemark(request.getRemark());
        invoice.setApplyRemark(request.getApplyRemark());

        // 收件信息
        invoice.setAddresseeName(request.getAddresseeName());
        invoice.setAddresseeMobile(request.getAddresseeMobile());
        invoice.setAddresseeAddress(request.getAddresseeAddress());
        invoice.setAddresseeEmail(request.getAddresseeEmail());

        return invoice;
    }

    /**
     * 创建开票明细
     */
    private List<InvoiceItemEntity> createInvoiceItems(Long invoiceId, List<InvoiceItemRequest> itemRequests) {
        List<InvoiceItemEntity> items = new ArrayList<>();

        for (InvoiceItemRequest itemRequest : itemRequests) {
            InvoiceItemEntity item = new InvoiceItemEntity();
            item.setInvoiceId(invoiceId);
            item.setBillId(itemRequest.getBillId());
            item.setInvoiceCategory(itemRequest.getInvoiceCategory());
            item.setFee(itemRequest.getFee());

            // 获取合同ID
            BillMasterEntity bill = billMasterRepository.findById(itemRequest.getBillId())
                    .orElseThrow(() -> new BusinessException("账单不存在"));
            item.setContractId(bill.getContractId());

            items.add(item);
        }

        return items;
    }

    /**
     * 更新账单已开票金额
     */
    private void updateBillInvoicedAmount(List<InvoiceItemRequest> items) {
        for (InvoiceItemRequest item : items) {
            BillMasterEntity bill = billMasterRepository.findById(item.getBillId())
                    .orElseThrow(() -> new BusinessException("账单不存在"));

            BigDecimal newInvoicedAmount = bill.getInvoicedAmount().add(item.getFee());
            bill.setInvoicedAmount(newInvoicedAmount);
            billMasterRepository.save(bill);
        }
    }

    /**
     * 释放已占用的开票金额
     */
    private void releaseInvoicedAmount(Long invoiceId) {
        List<InvoiceItemEntity> items = invoiceItemRepository.findByInvoiceIdAndDeletedFalse(invoiceId);

        for (InvoiceItemEntity item : items) {
            BillMasterEntity bill = billMasterRepository.findById(item.getBillId())
                    .orElseThrow(() -> new BusinessException("账单不存在"));

            BigDecimal newInvoicedAmount = bill.getInvoicedAmount().subtract(item.getFee());
            bill.setInvoicedAmount(newInvoicedAmount);
            billMasterRepository.save(bill);
        }
    }

    /**
     * 验证状态流转
     */
    private void validateStatusTransition(InvoiceStatus currentStatus, InvoiceStatus newStatus) {
        Map<InvoiceStatus, Set<InvoiceStatus>> allowedTransitions = Map.of(
            InvoiceStatus.PENDING, Set.of(InvoiceStatus.INVOICED, InvoiceStatus.RETURNED),
            InvoiceStatus.INVOICED, Set.of(), // 已开票状态不能再转换
            InvoiceStatus.RETURNED, Set.of()  // 已退回状态不能再转换
        );

        Set<InvoiceStatus> allowed = allowedTransitions.get(currentStatus);
        if (allowed == null || !allowed.contains(newStatus)) {
            throw new BusinessException(String.format("不允许从状态 %s 转换到 %s",
                    currentStatus.getDescription(), newStatus.getDescription()));
        }
    }

    /**
     * 生成开票编号
     */
    private String generateInvoiceSn() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmss"));
        String randomStr = String.format("%04d", (int)(Math.random() * 10000));
        return "INV" + dateStr + randomStr;
    }

    /**
     * 转换为可开票账单VO
     */
    private AvailableBillVO convertToAvailableBillVO(BillMasterEntity bill, BigDecimal availableAmount, List<String> availableCategories) {
        AvailableBillVO vo = new AvailableBillVO();
        vo.setBillId(bill.getId());
        vo.setBillNo(bill.getBillNo());
        vo.setBillMonth(bill.getBillMonth());
        vo.setTotalInvoiceAmount(bill.getTotalInvoiceAmount());
        vo.setInvoicedAmount(bill.getInvoicedAmount());
        vo.setAvailableAmount(availableAmount);
        vo.setAvailableCategories(availableCategories);

        // 设置客户名称
        try {
            CustomerWithInfo customer = customerManager.queryCustomer(bill.getCustomerId());
            if (customer != null) {
                vo.setCustomerName(customer.getName());
            }
        } catch (Exception e) {
            log.warn("获取客户信息失败: customerId={}", bill.getCustomerId(), e);
        }

        return vo;
    }

    /**
     * 转换为开票VO
     */
    private InvoiceVO convertToInvoiceVO(InvoiceEntity invoice, List<InvoiceItemEntity> items) {
        InvoiceVO vo = new InvoiceVO();
        BeanUtils.copyProperties(invoice, vo);
        vo.setStatusDesc(invoice.getStatus().getDescription());
        vo.setTypeDesc(invoice.getType().getDescription());

        // 转换明细
        List<InvoiceItemVO> itemVOs = items.stream()
                .map(this::convertToInvoiceItemVO)
                .toList();
        vo.setItems(itemVOs);

        return vo;
    }

    /**
     * 转换为开票明细VO
     */
    private InvoiceItemVO convertToInvoiceItemVO(InvoiceItemEntity item) {
        InvoiceItemVO vo = new InvoiceItemVO();
        BeanUtils.copyProperties(item, vo);

        // 设置账单信息
        try {
            BillMasterEntity bill = billMasterRepository.findById(item.getBillId()).orElse(null);
            if (bill != null) {
                vo.setBillNo(bill.getBillNo());
                vo.setBillMonth(bill.getBillMonth());
            }
        } catch (Exception e) {
            log.warn("获取账单信息失败: billId={}", item.getBillId(), e);
        }

        return vo;
    }

    /**
     * 构建开票预设信息
     */
    private InvoicePresetVO buildInvoicePresetVO(BusinessContractEntity contract,
                                                 BusinessContractConfigEntity contractConfig,
                                                 List<String> availableCategories,
                                                 List<AvailableBillVO> availableBills) {
        InvoicePresetVO preset = new InvoicePresetVO();

        // 设置基本信息
        preset.setCustomerId(contract.getCustomerId());
        preset.setContractId(contract.getId());
        preset.setContractName(contract.getName());
        preset.setSupplierCorporationId(contract.getSupplierCorporationId());

        // 设置客户名称
        try {
            CustomerWithInfo customer = customerManager.queryCustomer(contract.getCustomerId());
            if (customer != null) {
                preset.setCustomerName(customer.getName());
            }
        } catch (Exception e) {
            log.warn("获取客户信息失败: customerId={}", contract.getCustomerId(), e);
        }

        // 设置作业主体名称
        try {
            // 设置作业主体名称
            // 这里需要根据实际的作业主体实体来设置
            final SupplierCorporationEntity corporationEntity = corporationManager.requireCorporation(contract.getSupplierCorporationId());
            preset.setSupplierCorporationName(corporationEntity.getName());
        } catch (Exception e) {
            log.warn("获取作业主体信息失败: corporationId={}", contract.getSupplierCorporationId(), e);
        }

        // 设置发票类型选项
        List<InvoicePresetVO.InvoiceTypeOption> invoiceTypes = Arrays.asList(
                new InvoicePresetVO.InvoiceTypeOption(InvoiceType.GENERAL),
                new InvoicePresetVO.InvoiceTypeOption(InvoiceType.SPECIAL)
        );
        preset.setInvoiceTypes(invoiceTypes);

        // 设置发票信息
        InvoiceInfoVO invoiceInfo = new InvoiceInfoVO();
        invoiceInfo.setTitle(contractConfig.getInvoiceTitle());
        invoiceInfo.setTaxNo(contractConfig.getInvoiceTaxNo());
        invoiceInfo.setBankName(contractConfig.getInvoiceBankName());
        invoiceInfo.setBankAccount(contractConfig.getInvoiceBankAccount());
        invoiceInfo.setRegisterAddress(contractConfig.getInvoiceRegisterAddress());
        invoiceInfo.setCompanyTel(contractConfig.getInvoiceCompanyTel());
        invoiceInfo.setRemark(contractConfig.getInvoiceRemark());
        invoiceInfo.setAvailableCategories(availableCategories);
        preset.setInvoiceInfo(invoiceInfo);

        // 设置可开票明细列表
        preset.setAvailableBills(availableBills);

        return preset;
    }

    /**
     * 设置开票关联信息
     */
    private void setInvoiceRelatedInfo(InvoiceVO vo) {
        try {
            // 设置客户名称
            CustomerWithInfo customer = customerManager.queryCustomer(vo.getCustomerId());
            if (customer != null) {
                vo.setCustomerName(customer.getName());
            }

            // 设置合同名称
            BusinessContractEntity contract = em.find(BusinessContractEntity.class, vo.getContractId());
            if (contract != null) {
                vo.setContractName(contract.getName());
            }

            // 设置作业主体名称
            // 这里需要根据实际的作业主体实体来设置
            final SupplierCorporationEntity corporationEntity = corporationManager.requireCorporation(contract.getSupplierCorporationId());
            vo.setSupplierCorporationName(corporationEntity.getName());

        } catch (Exception e) {
            log.warn("设置开票关联信息失败: invoiceId={}", vo.getId(), e);
        }
    }
}