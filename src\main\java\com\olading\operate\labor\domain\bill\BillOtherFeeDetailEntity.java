package com.olading.operate.labor.domain.bill;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单其他费用明细表（第三层）- 其他费用明细
 */
@Entity
@Table(name = "t_bill_other_fee_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("账单其他费用明细表")
public class BillOtherFeeDetailEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Comment("账单主表ID")
    @Column(name = "bill_master_id", nullable = false)
    private Long billMasterId;

    @NotNull
    @Comment("账单分类ID")
    @Column(name = "bill_category_id", nullable = false)
    private Long billCategoryId;

    @NotNull
    @Comment("人员姓名")
    @Column(name = "labor_name", nullable = false, length = 50)
    private String laborName;

    @NotNull
    @Comment("身份证号")
    @Column(name = "id_card", nullable = false, length = 18)
    private String idCard;

    @NotNull
    @Comment("费用名称")
    @Column(name = "fee_name", nullable = false, length = 100)
    private String feeName;

    @NotNull
    @Comment("费用金额")
    @Column(name = "fee_amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal feeAmount = BigDecimal.ZERO;

    @NotNull
    @Comment("费用用途")
    @Column(name = "fee_purpose", nullable = false, length = 200)
    private String feePurpose;

    @NotNull
    @Comment("账单月份")
    @Column(name = "bill_month", nullable = false)
    private LocalDate billMonth;

    @Comment("产生时间")
    @Column(name = "occur_date")
    private LocalDate occurDate;

    @Comment("费用类别")
    @Column(name = "fee_category", length = 50)
    private String feeCategory;

    @Comment("费用说明")
    @Column(name = "fee_description", length = 500)
    private String feeDescription;

    @Comment("导入批次号")
    @Column(name = "import_batch_no", length = 50)
    private String importBatchNo;

    @Comment("备注")
    @Column(name = "remark", length = 200)
    private String remark;

    public BillOtherFeeDetailEntity(TenantInfo tenantInfo) {
        setTenant(tenantInfo);
    }

    protected BillOtherFeeDetailEntity() {
    }

}