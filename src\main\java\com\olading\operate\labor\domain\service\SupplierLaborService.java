package com.olading.operate.labor.domain.service;

import com.olading.operate.labor.app.web.biz.enums.EmpStatusEnum;
import com.olading.operate.labor.app.web.biz.labor.vo.ImportLaborRow;
import com.olading.operate.labor.app.web.biz.labor.vo.SupplierLaborVo;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.corporation.CorporationManager;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.LaborInfoRepository;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.domain.share.labor.SupplierLaborManager;
import com.olading.operate.labor.domain.share.protocol.CorporationProtocolEntity;
import com.olading.operate.labor.domain.share.protocol.LaborProtocolEntity;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborInsertParam;
import com.olading.operate.labor.domain.share.user.UserEntity;
import com.olading.operate.labor.domain.share.user.UserManager;
import com.olading.operate.labor.util.IDCardUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierLaborService {

    private final LaborInfoRepository laborInfoRepository;

    private final CustomerService customerService;

    private final BusinessContractService businessContractService;

    private final CorporationManager corporationManager;

    private final UserManager userManager;

    private final InfoSubmissionLaborService infoSubmissionLaborService;

    private final SupplierLaborManager supplierLaborManager;

    public Long createSupplierLabor(SupplierLaborVo param, Long supplierId, TenantInfo tenantInfo) {
        //处理身份证号 变成大写
        param.setIdCard(param.getIdCard().toUpperCase());
        return supplierLaborManager.createSupplierLabor(param, supplierId, tenantInfo);
    }

    public SupplierLaborVo getSupplierLaborDetail(Long id, Long supplierId) {
        return laborInfoRepository.findLaborInfoWithSupplierById(id, supplierId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSupplierLabor(Long supplierId, SupplierLaborVo param) {
        //身份证转大写
        param.setIdCard(param.getIdCard().toUpperCase());
        LaborInfoEntity labor = laborInfoRepository.findLaborById(param.getId());
        if (labor == null || !Objects.equals(labor.getSupplierId(), supplierId)) {
            throw new ApiException("修改的人员信息不存在", ApiException.SYSTEM_ERROR);
        }
        //查询平台人员信息
        SupplierLaborEntity supplierLabor = laborInfoRepository.findSupplierById(labor.getLaborId());
        if (supplierLabor == null) {
            throw new ApiException("修改的人员信息不存在", ApiException.SYSTEM_ERROR);
        }
        List<SupplierLaborEntity> cellphoneLabors = laborInfoRepository.findLaborByCellphone(param.getCellphone(), supplierId);
        if (CollectionUtils.isNotEmpty(cellphoneLabors)) {
            cellphoneLabors = cellphoneLabors.stream().filter(l -> !l.getIdCard().equals(param.getIdCard())).toList();
            if (CollectionUtils.isNotEmpty(cellphoneLabors)) {
                throw new ApiException("手机号已被" + cellphoneLabors.get(0).getName() +"占用", ApiException.SYSTEM_ERROR);
            }
        }
        boolean updateLabor = true;
        if (supplierLabor.getAuthStatus()) {
            if (param.getIdCard() != null && !param.getIdCard().equals(supplierLabor.getIdCard())
                    || param.getName() != null && !param.getName().equals(supplierLabor.getName())) {
                updateLabor = false;
            }
        }
        if (StringUtils.isNotBlank(param.getIdCard()) || StringUtils.isNotBlank(param.getName()) || StringUtils.isNotBlank(param.getCellphone())) {
            //查询协议信息，已发起协议不允许进行修改
            List<LaborProtocolEntity> protocol = laborInfoRepository.findLaborProtocolByCard(List.of(param.getIdCard()));
            if (CollectionUtils.isNotEmpty(protocol)) {
                updateLabor = false;
            }
            if (StringUtils.isNotBlank(param.getIdCard())) {
                IDCardUtil.checkIdNumber(param.getIdCard());
                Map<String, String> cerInfo = IDCardUtil.parseCertificateNo(param.getIdCard());
                supplierLabor.setBirthdayDate(cerInfo.get("birthday"));
            }
            if (StringUtils.isNotBlank(param.getName())) {
                supplierLabor.setName(param.getName());
            }
            if (StringUtils.isNotBlank(param.getCellphone())) {
                supplierLabor.setCellphone(param.getCellphone());
            }
            if (param.getIdCardPeriod() != null) {
                supplierLabor.setIdCardPeriod(param.getIdCardPeriod());
            }
            if (updateLabor) {
                //可更新的情况下更新，否则跳过更新
                laborInfoRepository.updateSupplierLaborInfo(supplierLabor);
            }

        }

        LaborInfoEntity laborInfo = param.toLaborInfo(labor);
        laborInfo.setId(param.getId());
        laborInfoRepository.updateLaborInfo(laborInfo);
    }
    public void verifyImportLaborRowAndImport(Long supplierId, List<ImportLaborRow> models, Map<OwnerType, Set<Long>> ownerTypeSetMap, TenantInfo tenant) {
        //查询作业主体列表、客户、服务合同列表
        List<ContractVo> contracts = businessContractService.queryContractBySupplier(supplierId);
        Map<String, ContractVo> contractVoMap = contracts.stream().collect(Collectors.toMap(ContractVo::getName, v -> v, (k1, k2) -> k1));
        Set<Long> contractIds = ownerTypeSetMap.get(OwnerType.CONTRACT);
        models.forEach(model -> {
            //服务合同校验
            if (StringUtils.isNotBlank(model.getContractName())) {
                ContractVo contractVo = contractVoMap.get(model.getContractName());
                if (contractVo == null) {
                    model.pushError(model.getContractName(), "服务合同不存在", "");
                } else {
                    if (!contractIds.contains(contractVo.getId())) {
                        model.pushError(model.getContractName(), "您没有当前服务合同的权限，请联系管理员添加", "");
                    }
                    model.setContractId(contractVo.getId());
                    model.setCustomerId(contractVo.getCustomerId());
                    model.setCorporationId(contractVo.getSupplierCorporationId());
                }
            }
            if (!model.isVerifyFail()) {
                try {
                    //将身份证转为大写
                    model.setIdCard(model.getIdCard().toUpperCase());
                    SupplierLaborVo laborVo = new SupplierLaborVo();
                    laborVo.setName(model.getName());
                    laborVo.setIdCard(model.getIdCard());
                    laborVo.setCellphone(model.getCellPhone());
                    laborVo.setContractId(model.getContractId());
                    laborVo.setCustomerId(model.getCustomerId());
                    laborVo.setCorporationId(model.getCorporationId());
                    laborVo.setBankCard(model.getBankCard());
                    laborVo.setCardBank(model.getCardBank());
                    supplierLaborManager.createSupplierLabor(laborVo, supplierId, tenant);
                } catch (ApiException e) {
                    model.pushCustomError(e.getMessage());
                }
            }
        });
    }

    public void deleteSupplierLabor(Long id) {
        laborInfoRepository.deleteLaborInfoById(id);
    }

    public void addSupplierLabor(Long supplierId, List<ImportLaborRow> successDataList, TenantInfo tenant) {
        successDataList.forEach(model -> {
            SupplierLaborEntity entity = new SupplierLaborEntity(tenant, supplierId);
            entity.setName(model.getName());
            entity.setIdCard(model.getIdCard());
            entity.setCellphone(model.getCellPhone());
            entity.setSupplierId(supplierId);
            laborInfoRepository.saveLabor(entity);
            LaborInfoEntity laborInfoEntity = new LaborInfoEntity();
            laborInfoEntity.setLaborId(entity.getId());
            laborInfoEntity.setJoinDate(LocalDate.now());
            laborInfoEntity.setContractId(model.getContractId());
            laborInfoEntity.setSupplierCorporationId(model.getCorporationId());
            laborInfoEntity.setEmpStatus(EmpStatusEnum.ON_THE_JOB.name());
            laborInfoEntity.setBankCard(model.getBankCard());
            laborInfoEntity.setCardBank(model.getCardBank());
            laborInfoEntity.setSupplierId(supplierId);
            laborInfoEntity.setCustomerId(model.getCustomerId());
            laborInfoRepository.saveLaborInfo(laborInfoEntity);
        });
    }

    public void userVerifySuccess(Long supplierId, UserEntity userEntity, String idCard, String name) {
        final List<SupplierLaborEntity> laborByCellphone = laborInfoRepository.findLaborByCellphone(userEntity.getCellphone(), supplierId);
        if (CollectionUtils.isEmpty(laborByCellphone)) {
            throw new ApiException("人员不存在", ApiException.API_PARAM_ERROR);
        }
        if (laborByCellphone.size() != 1) {
            throw new ApiException("身份信息不匹配,存在多个手机号关联人员，请联系服务人员处理", ApiException.API_PARAM_ERROR);
        }
        final SupplierLaborEntity labor = laborByCellphone.get(0);
        if (!StringUtils.equals(labor.getName(),name) || !StringUtils.equals(labor.getIdCard(),idCard)) {
            throw new ApiException("身份信息不匹配，请联系服务人员处理", ApiException.API_PARAM_ERROR);
        }
        userManager.identifyUser(userEntity.getId(), idCard, name);
        labor.setAuthStatus(true);
        labor.setAuthTime(LocalDateTime.now());
        laborInfoRepository.updateSupplierLaborInfo(labor);
    }

    public void updateLaborProtocol(CorporationProtocolEntity protocol) {
        List<LaborProtocolEntity> laborProtocol = laborInfoRepository.findLaborProtocolByCardAndCorporationId(List.of(protocol.getIdCard()), protocol.getSupplierCorporationId());
        LaborProtocolEntity entity = null;
        if (CollectionUtils.isNotEmpty(laborProtocol)) {
            entity = laborProtocol.get(0);
            entity.setProtocolStatus(protocol.getSignStatus().name());
            laborInfoRepository.updateLaborProtocol(entity);
        } else {
            SupplierLaborEntity labor = laborInfoRepository.findLaborByIdCard(protocol.getIdCard(), protocol.getSupplierId());
            entity = new LaborProtocolEntity();
            entity.setName(labor.getName());
            entity.setIdCard(protocol.getIdCard());
            entity.setSupplierId(protocol.getSupplierId());
            entity.setSupplierCorporationId(protocol.getSupplierCorporationId());
            entity.setProtocolStatus(protocol.getSignStatus().name());
            entity.setStartDate(protocol.getStartDate());
            entity.setEndDate(protocol.getEndDate());
            laborInfoRepository.saveLaborProtocol(entity);
        }
        try {
            InfoSubmissionLaborInsertParam param = new InfoSubmissionLaborInsertParam(protocol.getSupplierId(), protocol.getSupplierCorporationId(), protocol.getIdCard());
            infoSubmissionLaborService.insertInfoSubmissionLabor(param);
        } catch (Exception e) {
            log.error("同步人员信息报错", e);
        }

    }

    public SupplierLaborEntity getSupplierLaborByCellphone(Long supplierId, String cellphone) {
        final List<SupplierLaborEntity> laborByCellphone = laborInfoRepository.findLaborByCellphone(cellphone, supplierId);
        if (CollectionUtils.isEmpty(laborByCellphone)) {
            throw new ApiException("人员不存在", ApiException.API_PARAM_ERROR);
        }
        if (laborByCellphone.size() != 1) {
            throw new ApiException("身份信息不匹配,存在多个手机号关联人员，请联系服务人员处理", ApiException.API_PARAM_ERROR);
        }
        return laborByCellphone.get(0);
    }
}
