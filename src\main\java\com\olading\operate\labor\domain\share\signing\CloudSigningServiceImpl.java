package com.olading.operate.labor.domain.share.signing;


import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.olading.operate.labor.AppProperties;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.share.signing.enums.MethodType;
import com.olading.operate.labor.domain.share.signing.request.AuthPhone3Request;
import com.olading.operate.labor.domain.share.signing.request.AuthorizeRequest;
import com.olading.operate.labor.domain.share.signing.request.BaseRequest;
import com.olading.operate.labor.domain.share.signing.request.BuildPersonnelSignImgRequest;
import com.olading.operate.labor.domain.share.signing.request.BuildSignImgRequest;
import com.olading.operate.labor.domain.share.signing.request.CreateSignFileRequest;
import com.olading.operate.labor.domain.share.signing.request.CreateTemplateRequest;
import com.olading.operate.labor.domain.share.signing.request.EditTemplateRequest;
import com.olading.operate.labor.domain.share.signing.request.IdCardOcrRequest;
import com.olading.operate.labor.domain.share.signing.request.PrepareSignRequest;
import com.olading.operate.labor.domain.share.signing.request.QueryFileRequest;
import com.olading.operate.labor.domain.share.signing.request.QuerySignRequest;
import com.olading.operate.labor.domain.share.signing.request.QueryTemplateRequest;
import com.olading.operate.labor.domain.share.signing.request.SignRequest;
import com.olading.operate.labor.domain.share.signing.request.UploadTemplateFileRequest;
import com.olading.operate.labor.domain.share.signing.response.AuthPhone3Response;
import com.olading.operate.labor.domain.share.signing.response.AuthorizeResponse;
import com.olading.operate.labor.domain.share.signing.response.BaseCloudSigningResponse;
import com.olading.operate.labor.domain.share.signing.response.BuildPersonnelSignImgResponse;
import com.olading.operate.labor.domain.share.signing.response.BuildSignImgResponse;
import com.olading.operate.labor.domain.share.signing.response.CreateSignFileResponse;
import com.olading.operate.labor.domain.share.signing.response.CreateTemplateResponse;
import com.olading.operate.labor.domain.share.signing.response.EditTemplateResponse;
import com.olading.operate.labor.domain.share.signing.response.IdCardOcrResponse;
import com.olading.operate.labor.domain.share.signing.response.PrepareSignResponse;
import com.olading.operate.labor.domain.share.signing.response.QueryFileResponse;
import com.olading.operate.labor.domain.share.signing.response.QuerySignResponse;
import com.olading.operate.labor.domain.share.signing.response.QueryTemplateResponse;
import com.olading.operate.labor.domain.share.signing.response.SignResponse;
import com.olading.operate.labor.domain.share.signing.response.UploadTemplateFileMessage;
import com.olading.operate.labor.domain.share.signing.response.UploadTemplateFileResponse;
import com.olading.operate.labor.util.HttpRequestUtil;
import com.olading.operate.labor.util.JacksonBuilder;
import com.olading.operate.labor.util.MacUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class CloudSigningServiceImpl implements CloudSigningService {


    private ObjectMapper mapper = JacksonBuilder.build().setSerializationInclusion(JsonInclude.Include.NON_NULL);

    private AppProperties appProperties;

    public CloudSigningServiceImpl(AppProperties appProperties) {
        this.appProperties = appProperties;
    }


    @Override
    public BaseCloudSigningResponse<AuthorizeResponse> authorize(AuthorizeRequest request) {
        try {
            String body = execute(request, MethodType.AUTHORIZE);
            BaseCloudSigningResponse<AuthorizeResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            AuthorizeResponse authorizeResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), AuthorizeResponse.class);
            response.setData(authorizeResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }


    @Override
    public BaseCloudSigningResponse<CreateTemplateResponse> createTemplate(CreateTemplateRequest request) {
        try {
            if (request.getFileSize() == null || request.getFileSize() == 0L) {
                request.setFileSize(1L);
            }
            String body = execute(request, MethodType.CREATE_TEMPLATE);
            BaseCloudSigningResponse<CreateTemplateResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            CreateTemplateResponse createTemplateResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), CreateTemplateResponse.class);
            response.setData(createTemplateResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }


    @Override
    public UploadTemplateFileResponse uploadTemplateFile(ByteArrayOutputStream outputStream, UploadTemplateFileRequest request) throws IOException {
        UploadTemplateFileResponse response = new UploadTemplateFileResponse();
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(request.getUploadUrl());
        log.info("合同模板上传url:{}",request.getUploadUrl());
//        byte[] bytes = IOUtils.toByteArray(inputStream);
        httpPost.setEntity(new ByteArrayEntity(outputStream.toByteArray()));
        httpPost.setHeader("Content-type", "application/octet-stream;charset=UTF-8");
        httpPost.setHeader("Authorization", request.getToken());
        HttpResponse httpResponse = httpClient.execute(httpPost);
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        if (HttpStatus.SC_OK == statusCode) {
            String content = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
            UploadTemplateFileMessage message = JSONUtil.toBean(content, UploadTemplateFileMessage.class);
            response.setMessage(message);
            response.setSuccess(true);
        } else {
            response.setSuccess(false);
        }
        return response;
    }

    @Override
    public BaseCloudSigningResponse<EditTemplateResponse> editTemplate(EditTemplateRequest request) {
        try {
            String body = execute(request, MethodType.EDIT_TEMPLATE);
            BaseCloudSigningResponse<EditTemplateResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            EditTemplateResponse editTemplateResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), EditTemplateResponse.class);
            response.setData(editTemplateResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<CreateSignFileResponse> createSignFile(CreateSignFileRequest request) {
        try {
            String body = execute(request, MethodType.CREATE_SIGN_FILE);
            BaseCloudSigningResponse<CreateSignFileResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            CreateSignFileResponse createSignFileResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), CreateSignFileResponse.class);
            response.setData(createSignFileResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<PrepareSignResponse> prepareSign(PrepareSignRequest request) {
        try {
            String body = execute(request, MethodType.PREPARE_SIGN);
            BaseCloudSigningResponse<PrepareSignResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            PrepareSignResponse prepareSignResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), PrepareSignResponse.class);
            response.setData(prepareSignResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<SignResponse> sign(SignRequest request) {
        try {
            String body = execute(request, MethodType.SIGN);
            BaseCloudSigningResponse<SignResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            SignResponse signResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), SignResponse.class);
            response.setData(signResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<QueryTemplateResponse> queryTemplate(QueryTemplateRequest request) {
        try {
            String body = execute(request, MethodType.QUERY_TEMPLATE);
            BaseCloudSigningResponse<QueryTemplateResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            QueryTemplateResponse queryTemplateResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), QueryTemplateResponse.class);
            response.setData(queryTemplateResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<QueryFileResponse> queryFile(QueryFileRequest request) {
        try {
            String body = execute(request, MethodType.QUERY_FILE);
            BaseCloudSigningResponse<QueryFileResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            QueryFileResponse queryFileResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), QueryFileResponse.class);
            response.setData(queryFileResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<QuerySignResponse> querySign(QuerySignRequest request) {
        try {
            String body = execute(request, MethodType.QUERY_SIGN);
            BaseCloudSigningResponse<QuerySignResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            QuerySignResponse querySignResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), QuerySignResponse.class);
            response.setData(querySignResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<BuildSignImgResponse> buildSignImg(BuildSignImgRequest request) throws IOException {
        try {
            String body = execute(request, MethodType.BUILD_SIGN_IMG);
            BaseCloudSigningResponse<BuildSignImgResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            BuildSignImgResponse buildSignImgResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), BuildSignImgResponse.class);
            response.setData(buildSignImgResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<BuildPersonnelSignImgResponse> buildPersonnelSignImg(BuildPersonnelSignImgRequest request) throws IOException {
        try {
            String body = execute(request, MethodType.BUILD_PERSONNEL_SIGN_IMG);
            BaseCloudSigningResponse<BuildPersonnelSignImgResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            BuildPersonnelSignImgResponse buildPersonnelSignImgResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), BuildPersonnelSignImgResponse.class);
            response.setData(buildPersonnelSignImgResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<IdCardOcrResponse> idCardOcr(IdCardOcrRequest request) {
        try {
            String body = execute(request, MethodType.ID_CARD_OCR);
            BaseCloudSigningResponse<IdCardOcrResponse> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            IdCardOcrResponse idCardOcrResponse = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), IdCardOcrResponse.class);
            response.setData(idCardOcrResponse);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public BaseCloudSigningResponse<AuthPhone3Response> authPhone3(AuthPhone3Request request) {
        try {
            String body = execute(request, MethodType.AUTH_PHONE3);
            BaseCloudSigningResponse<AuthPhone3Response> response = JSONUtil.toBean(body, BaseCloudSigningResponse.class);
            AuthPhone3Response authPhone3Response = JSONUtil.toBean(JSONUtil.toJsonStr(response.getData()), AuthPhone3Response.class);
            response.setData(authPhone3Response);
            return response;
        } catch (Exception e) {
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    private String execute(BaseRequest request, MethodType methodType) {
        String method = methodType.name();
        request.setTenantId(appProperties.getSigningTenantId());
        String result = null;
        try {
            Map<String, Object> req = new HashMap<>();
            req.put("name", method);
            req.put("client_key", appProperties.getSigningClientKey());
            req.put("timestamp", LocalDateTime.now());
            req.put("data", request);
            String sign = MacUtils.sign("HmacSHA256", appProperties.getSigningSecretKey(), mapper.writeValueAsString(req));
            Map<String, String> params = new HashMap<>();
            params.put("sign", sign);
            params.put("req", mapper.writeValueAsString(req));
            log.info("接口[{}]请求参数:{}", method, mapper.writeValueAsString(params));
            result = HttpRequestUtil.post(appProperties.getSigningApiUrl(), params);
            log.info("接口[{}]调用响应参数:{}", method, result);
            JavaType mapType = mapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class);
            Map<String, String> resultMap = null;
            try {
                resultMap = mapper.readValue(result, mapType);
            } catch (IOException e) {
                throw new IllegalArgumentException(e);
            }
            return JacksonBuilder.json(resultMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ApiException(e.getMessage(), ApiException.SYSTEM_ERROR);
        }
    }

    @Override
    public InputStream downLoadByUrl(String url) {
//        String httpVersion = appProperties.getHttpVersion();
//        String targetHttpVersion = appProperties.getTargetHttpVersion();
//        log.info("云签返回地址:{},httpVersion:{},targetHttpVersion:{}", url, httpVersion, targetHttpVersion);
//        if(StringUtils.isNotBlank(httpVersion) && StringUtils.isNotBlank(targetHttpVersion)){
//            url = url.replace(httpVersion,targetHttpVersion);
//        }
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-type", "application/octet-stream;charset=UTF-8");
            HttpResponse httpResponse = httpClient.execute(httpPost);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            log.info("下载云签合同文档响应码:{}", statusCode);
            if(statusCode != 200){
                throw new ApiException("下载云签文档，响应码非200", ApiException.SYSTEM_ERROR);
            }
            return httpResponse.getEntity().getContent();
        } catch (IOException e) {
            throw new ApiException("下载云签合同文档异常:", ApiException.SYSTEM_ERROR);
        }
    }




}
