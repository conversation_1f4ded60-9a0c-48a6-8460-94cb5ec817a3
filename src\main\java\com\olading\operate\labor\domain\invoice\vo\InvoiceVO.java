package com.olading.operate.labor.domain.invoice.vo;

import com.olading.operate.labor.domain.invoice.InvoiceStatus;
import com.olading.operate.labor.domain.invoice.InvoiceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "开票申请信息")
public class InvoiceVO {
    
    @Schema(description = "开票ID")
    private Long id;
    
    @Schema(description = "申请编号")
    private String sn;
    
    @Schema(description = "供应商ID")
    private Long supplierId;
    
    @Schema(description = "客户ID")
    private Long customerId;
    
    @Schema(description = "客户名称")
    private String customerName;
    
    @Schema(description = "作业主体ID")
    private Long supplierCorporationId;
    
    @Schema(description = "作业主体名称")
    private String supplierCorporationName;
    
    @Schema(description = "合同ID")
    private Long contractId;
    
    @Schema(description = "合同名称")
    private String contractName;
    
    @Schema(description = "开票状态")
    private InvoiceStatus status;
    
    @Schema(description = "开票状态描述")
    private String statusDesc;
    
    @Schema(description = "发票类型")
    private InvoiceType type;
    
    @Schema(description = "发票类型描述")
    private String typeDesc;
    
    @Schema(description = "开票金额")
    private BigDecimal fee;
    
    @Schema(description = "发票抬头")
    private String title;
    
    @Schema(description = "纳税识别号")
    private String taxNo;
    
    @Schema(description = "开户行")
    private String bankName;
    
    @Schema(description = "银行账号")
    private String bankAccount;
    
    @Schema(description = "注册地址")
    private String registerAddress;
    
    @Schema(description = "企业电话")
    private String companyTel;
    
    @Schema(description = "发票备注")
    private String remark;
    
    @Schema(description = "申请备注")
    private String applyRemark;
    
    @Schema(description = "收件人姓名")
    private String addresseeName;
    
    @Schema(description = "收件人电话")
    private String addresseeMobile;
    
    @Schema(description = "收件人地址")
    private String addresseeAddress;
    
    @Schema(description = "收件人邮箱")
    private String addresseeEmail;
    
    @Schema(description = "退回原因")
    private String backReason;
    
    @Schema(description = "发票文件")
    private String invoiceFile;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;
    
    @Schema(description = "开票明细列表")
    private List<InvoiceItemVO> items;
}