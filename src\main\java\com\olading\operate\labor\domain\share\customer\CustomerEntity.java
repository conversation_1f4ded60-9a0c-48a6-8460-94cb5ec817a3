package com.olading.operate.labor.domain.share.customer;

import com.olading.operate.labor.domain.BaseEntity;
import com.olading.operate.labor.domain.TenantInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Comment("客户信息表")
@Entity
@Table(name = "t_customer", schema = "olading_labor")
@AttributeOverrides({
        @AttributeOverride(name = "version", column = @Column(name = "version"))
})
public class CustomerEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Comment("灵工平台id")
    @Column(name = "supplier_id")
    private Long supplierId;

    @Size(max = 200)
    @NotNull
    @Comment("客户名称")
    @Column(name = "name", nullable = false, length = 200)
    private String name;

    @Comment("企业信息ID")
    @Column(name = "enterprise_info_id")
    private Long enterpriseInfoId;

    @Size(max = 64)
    @Comment("编号")
    @Column(name = "sn", length = 64)
    private String sn;

    @Size(max = 64)
    @Comment("简称")
    @Column(name = "short_name", length = 64)
    private String shortName;

    @Size(max = 20)
    @Comment("状态:1-未合作;2-合作中;3-停止合作")
    @Column(name = "status", length = 20)
    private String status;

    @Size(max = 64)
    @Comment("地区id")
    @Column(name = "region_id", length = 64)
    private String regionId;

    @Size(max = 160)
    @Comment("详细地址")
    @Column(name = "address", length = 160)
    private String address;

    @Size(max = 20)
    @Comment("行业")
    @Column(name = "industry", length = 20)
    private String industry;

    @Size(max = 20)
    @Comment("性质")
    @Column(name = "type", length = 20)
    private String type;

    @Size(max = 20)
    @Comment("规模")
    @Column(name = "size", length = 20)
    private String size;

    @Size(max = 64)
    @Comment("来源")
    @Column(name = "source", length = 64)
    private String source;

    @Size(max = 20)
    @Comment("销售负责人姓名")
    @Column(name = "sales_name", length = 20)
    private String salesName;

    @Size(max = 20)
    @Comment("销售负责人电信")
    @Column(name = "service_mobile", length = 20)
    private String serviceMobile;

    @Size(max = 64)
    @Comment("客户联系人姓名")
    @Column(name = "contact_name", length = 64)
    private String contactName;

    @Size(max = 64)
    @Comment("客户联系人电话")
    @Column(name = "contact_mobile", length = 64)
    private String contactMobile;

    @Size(max = 200)
    @Comment("备注")
    @Column(name = "remark", length = 200)
    private String remark;

    @Comment("是否禁用")
    @Column(name = "disabled")
    private Boolean disabled;

    @Comment("用户ID")
    @Column(name = "user_id")
    private Long userId;

    public CustomerEntity(TenantInfo tenantInfo) {
        if(tenantInfo != null){
            setTenant(tenantInfo);
        }
    }

    public CustomerEntity() {
    }
}