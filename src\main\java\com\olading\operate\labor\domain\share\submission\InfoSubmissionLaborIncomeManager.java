package com.olading.operate.labor.domain.share.submission;

import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.util.ThreadPoolUtil;
import com.olading.operate.labor.app.web.biz.enums.PersonalIncomeTaxDeclareStatusEnum;
import com.olading.operate.labor.app.web.biz.enums.CertificateTypeEnum;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.file.FileManager;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborIncomeVo;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.QSalaryDetailEntity;
import com.olading.operate.labor.domain.salary.QSalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.share.labor.LaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.QLaborInfoEntity;
import com.olading.operate.labor.domain.share.labor.SupplierLaborEntity;
import com.olading.operate.labor.domain.share.labor.QSupplierLaborEntity;
import com.olading.operate.labor.domain.corporation.SupplierCorporationEntity;
import com.olading.operate.labor.domain.corporation.QSupplierCorporationEntity;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional
@Component
@RequiredArgsConstructor
@Slf4j
public class InfoSubmissionLaborIncomeManager {

    private final EntityManager em;
    private final FileManager fileManager;
    private final PlatformTransactionManager transactionManager;

    private static final String LABOR_INCOME_TEMPLATE = "/template/人员收入信息报送.xls";

    /**
     * 新增人员收入信息报送记录
     */
    public InfoSubmissionLaborIncomeEntity addInfoSubmissionLaborIncome(TenantInfo tenantInfo, InfoSubmissionLaborIncomeVo vo) {
        // 创建实体
        InfoSubmissionLaborIncomeEntity entity = new InfoSubmissionLaborIncomeEntity(tenantInfo);
        copyToEntity(vo, entity);
        entity.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATING.name());
        entity = em.merge(entity);
        em.flush();

        // 异步生成文件
        final Long entityId = entity.getId();
        ThreadPoolUtil.executeCommonThreadPool(() -> {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            TransactionStatus status = transactionManager.getTransaction(def);
            
            try {
                // 生成人员收入信息报送文件
                byte[] fileContent = generateLaborIncomeInfoFile(vo.getSupplierCorporationId(), vo.getStartDate(), vo.getEndDate());

                // 保存文件
                String fileId = fileManager.saveTemp(
                    "人员收入信息报送_" + entityId + ".xls",
                    new ByteArrayInputStream(fileContent),
                    OwnerType.SUPPLIER,
                    String.valueOf(vo.getSupplierId())
                );
                
                // 更新记录状态和文件ID
                InfoSubmissionLaborIncomeEntity updateEntity = em.find(InfoSubmissionLaborIncomeEntity.class, entityId);
                if (updateEntity != null) {
                    updateEntity.setFileId(fileId);
                    updateEntity.setStatus(PersonalIncomeTaxDeclareStatusEnum.GENERATED.name());
                    em.merge(updateEntity);
                }
                
                transactionManager.commit(status);
                log.info("人员收入信息报送文件生成成功，记录ID: {}, 文件ID: {}", entityId, fileId);
                
            } catch (Exception e) {
                transactionManager.rollback(status);
                log.error("异步生成人员收入信息报送文件失败，记录ID: {}，状态保持为生成中", entityId, e);
            }
        });

        return entity;
    }

    /**
     * 更新人员收入信息报送记录
     */
    public InfoSubmissionLaborIncomeEntity updateInfoSubmissionLaborIncome(InfoSubmissionLaborIncomeVo vo) {
        InfoSubmissionLaborIncomeEntity entity = getInfoSubmissionLaborIncomeById(vo.getId());
        copyToEntity(vo, entity);
        return em.merge(entity);
    }

    /**
     * 查询人员收入信息报送记录详情
     */
    public InfoSubmissionLaborIncomeVo queryInfoSubmissionLaborIncome(Long id) {
        InfoSubmissionLaborIncomeEntity entity = getInfoSubmissionLaborIncomeById(id);
        InfoSubmissionLaborIncomeVo vo = new InfoSubmissionLaborIncomeVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    /**
     * 删除人员收入信息报送记录
     */
    public void deleteInfoSubmissionLaborIncome(Long id) {
        InfoSubmissionLaborIncomeEntity entity = getInfoSubmissionLaborIncomeById(id);
        em.remove(entity);
    }

    /**
     * 根据ID获取人员收入信息报送记录
     */
    public InfoSubmissionLaborIncomeEntity getInfoSubmissionLaborIncomeById(Long id) {
        InfoSubmissionLaborIncomeEntity entity = em.find(InfoSubmissionLaborIncomeEntity.class, id);
        if (entity == null) {
            throw new BusinessException("人员收入信息报送记录不存在");
        }
        return entity;
    }

    /**
     * 生成人员收入信息报送文件内容
     */
    private byte[] generateLaborIncomeInfoFile(Long supplierCorporationId, String startDate, String endDate) {
        try {
            // 统一转换时间范围
            LocalDateTime startDateTime = parseDateTime(startDate, true);
            LocalDateTime endDateTime = parseDateTime(endDate, false);

            // 查询工资明细数据
            List<SalaryDetailEntity> salaryDetails = querySalaryDetailForLaborIncome(supplierCorporationId, startDateTime, endDateTime);

            // 读取模板文件
            Workbook workbook = new HSSFWorkbook(new DefaultResourceLoader().getResource(LABOR_INCOME_TEMPLATE).getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            // 填充数据到模板
            fillLaborIncomeTemplateData(sheet, salaryDetails, supplierCorporationId, startDateTime, endDateTime);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();

            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("生成人员收入信息报送文件失败", e);
            throw new RuntimeException("生成文件失败: " + e.getMessage());
        }
    }

    /**
     * 解析日期字符串为LocalDateTime
     */
    private LocalDateTime parseDateTime(String dateStr, boolean isStartDate) {
        String timeStr = isStartDate ? " 00:00:00" : " 23:59:59";
        return LocalDateTime.parse(dateStr + timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 查询工资明细数据用于人员收入信息报送
     * 参考SalaryManager.querySalaryDetailForTaxDeclare，但使用时间范围查询
     */
    private List<SalaryDetailEntity> querySalaryDetailForLaborIncome(Long supplierCorporationId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        // 查询符合条件的所有明细记录
        List<SalaryDetailEntity> allDetails = new JPAQueryFactory(em)
                .select(detail)
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.supplierCorporationId.eq(supplierCorporationId)
                        .and(detail.createTime.between(startDateTime, endDateTime))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetch();

        // 按身份证号分组，每个身份证号只保留一条记录（去重）
        Map<String, SalaryDetailEntity> uniqueDetails = allDetails.stream()
                .collect(Collectors.toMap(
                        SalaryDetailEntity::getIdCard,
                        entity -> entity,
                        (existing, replacement) -> existing
                ));

        return uniqueDetails.values().stream().collect(Collectors.toList());
    }

    /**
     * 填充人员收入信息报送模板数据
     */
    private void fillLaborIncomeTemplateData(Sheet sheet, List<SalaryDetailEntity> salaryDetails, Long supplierCorporationId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        if (salaryDetails == null || salaryDetails.isEmpty()) {
            return;
        }

        int rowIndex = 4;
        int sequenceNumber = 1;

        // 查询作业主体名称
        String supplierCorporationName = getSupplierCorporationName(supplierCorporationId);

        for (SalaryDetailEntity salaryDetail : salaryDetails) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }

            // A列：序号
            setCellValue(row, 0, String.valueOf(sequenceNumber));

            // E列：姓名
            setCellValue(row, 4, salaryDetail.getName());

            // F列：证件类型
            setCellValue(row, 5, CertificateTypeEnum.ID_CARD.getName());

            // G列：身份证号
            setCellValue(row, 6, salaryDetail.getIdCard());

            // H列：户籍城市
            String householdCity = getHouseholdCity(salaryDetail.getIdCard(), supplierCorporationId);
            setCellValue(row, 7, householdCity);

            // I列：作业主体名称
            setCellValue(row, 8, supplierCorporationName);

            // J列：姓名（重复）
            setCellValue(row, 9, salaryDetail.getName());

            // K列：supplierCorporationId_哈希的身份证号
            String hashedIdCard = supplierCorporationId + "_" + DigestUtils.md5Hex(salaryDetail.getIdCard());
            setCellValue(row, 10, hashedIdCard);

            // 计算汇总数据（需要在指定时间范围内）
            BigDecimal totalPayableAmount = calculateTotalPayableAmount(salaryDetail.getIdCard(), supplierCorporationId, startDateTime, endDateTime);
            int totalCount = calculateTotalCount(salaryDetail.getIdCard(), supplierCorporationId, startDateTime, endDateTime);

            // U列：sum(payable_amount)
            setCellValue(row, 20, totalPayableAmount.toString());

            // W列：sum(payable_amount)
            setCellValue(row, 22, totalPayableAmount.toString());

            // Z列：sum(payable_amount)
            setCellValue(row, 25, totalPayableAmount.toString());

            // AE列：count(*)
            setCellValue(row, 30, String.valueOf(totalCount));

            rowIndex++;
            sequenceNumber++;
        }
    }

    /**
     * 获取作业主体名称
     */
    private String getSupplierCorporationName(Long supplierCorporationId) {
        QSupplierCorporationEntity corporation = QSupplierCorporationEntity.supplierCorporationEntity;
        SupplierCorporationEntity entity = new JPAQueryFactory(em)
                .select(corporation)
                .from(corporation)
                .where(corporation.id.eq(supplierCorporationId))
                .fetchOne();
        return entity != null ? entity.getName() : "";
    }

    /**
     * 获取户籍城市
     */
    private String getHouseholdCity(String idCard, Long supplierCorporationId) {
        QLaborInfoEntity laborInfo = QLaborInfoEntity.laborInfoEntity;
        QSupplierLaborEntity supplierLabor = QSupplierLaborEntity.supplierLaborEntity;

        LaborInfoEntity entity = new JPAQueryFactory(em)
                .select(laborInfo)
                .from(laborInfo)
                .join(supplierLabor).on(laborInfo.laborId.eq(supplierLabor.id))
                .where(laborInfo.supplierCorporationId.eq(supplierCorporationId)
                        .and(supplierLabor.idCard.eq(idCard)))
                .fetchFirst();

        return entity != null ? entity.getHouseholdCity() : "";
    }

    /**
     * 计算指定时间范围内的总应发金额
     */
    private BigDecimal calculateTotalPayableAmount(String idCard, Long supplierCorporationId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        BigDecimal totalAmount = new JPAQueryFactory(em)
                .select(detail.payableAmount.sum())
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.idCard.eq(idCard)
                        .and(detail.supplierCorporationId.eq(supplierCorporationId))
                        .and(detail.createTime.between(startDateTime, endDateTime))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetchOne();

        return totalAmount != null ? totalAmount : BigDecimal.ZERO;
    }

    /**
     * 计算指定时间范围内的总记录数
     */
    private int calculateTotalCount(String idCard, Long supplierCorporationId, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        QSalaryDetailEntity detail = QSalaryDetailEntity.salaryDetailEntity;
        QSalaryStatementEntity statement = QSalaryStatementEntity.salaryStatementEntity;

        Long count = new JPAQueryFactory(em)
                .select(detail.count())
                .from(detail)
                .join(statement).on(detail.salaryStatementId.eq(statement.id))
                .where(detail.idCard.eq(idCard)
                        .and(detail.supplierCorporationId.eq(supplierCorporationId))
                        .and(detail.createTime.between(startDateTime, endDateTime))
                        .and(statement.status.eq(SalaryStatementStatus.CONFIRMED)))
                .fetchOne();

        return count != null ? count.intValue() : 0;
    }

    /**
     * 设置单元格值
     */
    private void setCellValue(Row row, int columnIndex, String value) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }
        cell.setCellValue(value != null ? value : "");
    }

    /**
     * 复制VO到Entity
     */
    private void copyToEntity(InfoSubmissionLaborIncomeVo vo, InfoSubmissionLaborIncomeEntity entity) {
        if (vo.getSupplierCorporationId() != null) {
            entity.setSupplierCorporationId(vo.getSupplierCorporationId());
        }
        if (vo.getSupplierId() != null) {
            entity.setSupplierId(vo.getSupplierId());
        }
        if (vo.getStartDate() != null) {
            entity.setStartDate(vo.getStartDate());
        }
        if (vo.getEndDate() != null) {
            entity.setEndDate(vo.getEndDate());
        }
        if (vo.getStatus() != null) {
            entity.setStatus(vo.getStatus());
        }
        if (vo.getFileId() != null) {
            entity.setFileId(vo.getFileId());
        }
    }
}
