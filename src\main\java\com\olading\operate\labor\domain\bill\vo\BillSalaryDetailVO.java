package com.olading.operate.labor.domain.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单薪酬明细视图对象
 */
@Data
@Schema(description = "账单薪酬明细信息")
public class BillSalaryDetailVO {

    @Schema(description = "明细ID")
    private Long id;

    @Schema(description = "账单主表ID")
    private Long billMasterId;

    @Schema(description = "账单分类ID")
    private Long billCategoryId;

    @Schema(description = "薪酬明细ID")
    private Long salaryDetailId;

    @Schema(description = "薪酬批次ID")
    private Long salaryBatchId;

    @Schema(description = "人员姓名")
    private String laborName;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "应发工资")
    private BigDecimal grossSalary;

    @Schema(description = "实发工资")
    private BigDecimal netSalary;

    @Schema(description = "应缴个税")
    private BigDecimal incomeTax;

    @Schema(description = "应缴增值税")
    private BigDecimal vatTax;

    @Schema(description = "应缴附加税")
    private BigDecimal additionalTax;

    @Schema(description = "账单月份")
    private LocalDate billMonth;

    @Schema(description = "工资所属期")
    private String salaryPeriod;

    @Schema(description = "备注")
    private String remark;


    @Schema(description = "城市维护建设附加税")
    private BigDecimal urbanConstructionTax;


    @Schema(description = "教育费附加税")
    private BigDecimal educationSurcharge;


    @Schema(description = "地方教育附加税")
    private BigDecimal localEducationSurcharge;


    @Schema(description = "本次免税收入")
    private BigDecimal taxFreeIncome;


    @Schema(description = "本次依法确定的其他扣除")
    private BigDecimal otherDeductions;


    @Schema(description = "本次减免税额")
    private BigDecimal taxReliefAmount;


    @Schema(description = "累计收入")
    private BigDecimal accumulatedIncome;


    @Schema(description = "累计费用（累计收入*20%）")
    private BigDecimal accumulatedExpenses;


    @Schema(description = "累计免税收入 (累加)")
    private BigDecimal accumulatedTaxFreeIncome;


    @Schema(description = "累计依法确定的其他扣除 (累加)")
    private BigDecimal accumulatedOtherDeductions;


    @Schema(description = "累计减免税额 (累加)")
    private BigDecimal accumulatedTaxRelief;


    @Schema(description = "累计应纳税所得额")
    private BigDecimal accumulatedTaxableAmount;


    @Schema(description = "累计应纳税额")
    private BigDecimal accumulatedTaxAmount;


    @Schema(description = "累计已预缴税额,累计已报税的收入")
    private BigDecimal accumulatedPrepaidTax;


    @Schema(description = "本期应预扣预缴税额（当前月份累计）")
    private BigDecimal currentTaxAmount;


    @Schema(description = "本次应预扣预缴税额")
    private BigDecimal currentWithholdingTax;
}