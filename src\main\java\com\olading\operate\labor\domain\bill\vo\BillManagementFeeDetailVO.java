package com.olading.operate.labor.domain.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单管理费明细视图对象
 */
@Data
@Schema(description = "账单管理费明细信息")
public class BillManagementFeeDetailVO {

    @Schema(description = "明细ID")
    private Long id;

    @Schema(description = "账单主表ID")
    private Long billMasterId;

    @Schema(description = "账单分类ID")
    private Long billCategoryId;

    @Schema(description = "人员姓名")
    private String laborName;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "收费项目")
    private String feeItem;

    @Schema(description = "管理费金额")
    private BigDecimal managementFeeAmount;

    @Schema(description = "账单月份")
    private LocalDate billMonth;

    @Schema(description = "计算基数")
    private BigDecimal calculationBase;

    @Schema(description = "计算费率")
    private BigDecimal calculationRate;

    @Schema(description = "计算规则")
    private String calculationRule;

    @Schema(description = "备注")
    private String remark;
}