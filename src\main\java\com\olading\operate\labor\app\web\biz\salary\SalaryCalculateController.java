package com.olading.operate.labor.app.web.biz.salary;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.beans.Beans;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.query.WebApiQueryService;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.app.web.biz.enums.EnumTaxCalculationMethod;
import com.olading.operate.labor.app.web.biz.salary.vo.*;
import com.olading.operate.labor.config.NullIfEmptyInSetDeserializer;
import com.olading.operate.labor.config.ValidMultipartFileArray;
import com.olading.operate.labor.domain.ApiException;
import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.query.PreviousIncomeDeductionQuery;
import com.olading.operate.labor.domain.query.SalaryDetailQuery;
import com.olading.operate.labor.domain.query.SalaryQuery;
import com.olading.operate.labor.domain.salary.SalaryDetailEntity;
import com.olading.operate.labor.domain.salary.SalaryManager;
import com.olading.operate.labor.domain.salary.SalaryStatementEntity;
import com.olading.operate.labor.domain.salary.SalaryStatementStatus;
import com.olading.operate.labor.domain.salary.vo.PreviousIncomeDeductionVO;
import com.olading.operate.labor.domain.salary.vo.SalaryVO;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.service.SalaryCalculateService;
import com.olading.operate.labor.domain.share.contract.BusinessContractManager;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.info.OwnerType;
import com.olading.operate.labor.util.RedisUtils;
import com.olading.operate.labor.util.excel.ExcelResult;
import com.olading.operate.labor.util.excel.ExcelRow;
import com.olading.operate.labor.util.excel.ExcelWriter;
import com.olading.operate.labor.util.excel.Excels;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.RegEx;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

@Tag(name = "薪酬计算相关接口")
@RestController
@RequestMapping("/api/supplier/salary")
@RequiredArgsConstructor
@Slf4j
public class SalaryCalculateController extends BusinessController {
    private final SalaryManager salaryManager;
    private final QueryService queryService;
    private final WebApiQueryService webApiQueryService;
    private final SalaryCalculateService salaryCalculateService;
    private final BusinessContractManager businessContractManager;
    private final RedisUtils redisUtils;



    private static final String CONTENT_TYPE = "application/vnd.ms-excel";
    private static final String CONTENT_DISPOSITION = "attachment;filename=";
    private static final String SYSTEM_CALCULATION_TEMPLATE = "/template/上传工资表模版-系统预算.xlsx";
    private static final String PREVIOUS_INCOME_IMPORT_TEMPLATE = "/template/上期收入减除导入模版.xlsx";

    @Operation(summary = "薪酬计算-列表")
    @PostMapping(value = "listPayroll")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_CALCULATE)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<SalaryVO> listSalary(@RequestBody QueryFilter<WebSalaryFilters> request) {
        QueryFilter<SalaryQuery.Filters> filter = request.convert(WebSalaryFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        filter.setWithDeleted(false);
        filter.getFilters().setSupplierId(currentSupplierId());
        DataSet<SalaryVO> ds = queryService.querySalary(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "薪酬计算-确认算税结果")
    @PostMapping(value = "confirmPayroll")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_CALCULATE)
    public WebApiResponse<Void> confirmSalary(@RequestBody SalaryParams request) {

        SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(request.getId());
        if (salaryStatementEntity == null) {
            throw ApiException.paramError("工资表不存在");
        }

        final Set<Long> contractIds = currentDataScope().get(OwnerType.CONTRACT);
        if(salaryStatementEntity.getSupplierId() != currentSupplierId() || !contractIds.contains(salaryStatementEntity.getContractId())){
            throw ApiException.paramError("无操作权限");
        }
        salaryCalculateService.confirmStatement(salaryStatementEntity.getId());
        return WebApiResponse.success();
    }

    @Operation(summary = "薪酬计算-删除")
    @PostMapping(value = "deletePayroll")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_CALCULATE)
    public WebApiResponse<Void> deleteSalary(@RequestBody SalaryParams request) {
        final Set<Long> contractIds = currentDataScope().get(OwnerType.CONTRACT);
        long currentSupplierId = currentSupplierId();
        salaryCalculateService.deleteSalary(request.getId(), currentSupplierId, contractIds);
        return WebApiResponse.success();
    }

    @Operation(summary = "薪酬计算-查看")
    @PostMapping(value = "listPayrollDetail")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_CALCULATE)
    public WebApiQueryResponse<SalaryDetailEntity> listSalaryDetail(@RequestBody SalaryParams request) {

        SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(request.getId());
        if (salaryStatementEntity == null) {
            throw ApiException.paramError("工资表不存在");
        }
        final Set<Long> contractIds = currentDataScope().get(OwnerType.CONTRACT);
        if(salaryStatementEntity.getSupplierId() != currentSupplierId() || !contractIds.contains(salaryStatementEntity.getContractId())){
            throw ApiException.paramError("无操作权限");
        }
        List<SalaryDetailEntity> salaryDetails = salaryManager.querySalaryDetailByStatementId(request.getId());
        return WebApiQueryResponse.success(salaryDetails, salaryDetails.size());
    }


    @Operation(summary = "发薪超10w人员统计")
    @PostMapping(value = "salaryOverStaffCount")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_CALCULATE)
    public WebApiResponse<List<String>> salaryOverStaffCount(@RequestBody SalaryParams request) {
        SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(request.getId());
        if (salaryStatementEntity == null) {
            throw ApiException.paramError("工资表不存在");
        }
        final Set<Long> contractIds = currentDataScope().get(OwnerType.CONTRACT);
        if(salaryStatementEntity.getSupplierId() != currentSupplierId() || !contractIds.contains(salaryStatementEntity.getContractId())){
            throw ApiException.paramError("无操作权限");
        }
        List<String> list =salaryCalculateService.salaryOverStaffCount(request.getId());
        return WebApiResponse.success(list);
    }


    @Operation(summary = "薪酬计算-下载")
    @PostMapping(value = "payRollDetailDownload")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_CALCULATE)
    public void payRollDetailDownload(@RequestBody SalaryParams request, HttpServletResponse response) {
        SalaryStatementEntity salaryStatementEntity = salaryManager.querySalaryStatement(request.getId());
        if (salaryStatementEntity == null) {
            throw ApiException.paramError("工资表不存在");
        }
        final Set<Long> contractIds = currentDataScope().get(OwnerType.CONTRACT);
        if(salaryStatementEntity.getSupplierId() != currentSupplierId() || !contractIds.contains(salaryStatementEntity.getContractId())){
            throw ApiException.paramError("无操作权限");
        }
        QueryFilter<SalaryDetailQuery.Filters> filter = new QueryFilter<>();
        filter.getFilters().setSupplierCorporationId(request.getId());
        filter.sort("id", Direction.DESCENDING);
        downloadExcel(response,
                "业务主体列表.zip",
                webApiQueryService.exportSalaryDetail(),
                filter);
    }

    @Operation(summary = "薪酬计算-上期收入减除查询")
    @PostMapping(value = "listPreviousIncomeDeduction")
    @AuthorityDataScopGuard(
            query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CORPORATION, spel = "#request.filters.supplierCorporationIds")
    })
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_CALCULATE)
    public WebApiQueryResponse<PreviousIncomeDeductionVO> listPreviousIncomeDeduction(@RequestBody QueryFilter<WebPreviousIncomeDeductionFilters> request) {
        QueryFilter<PreviousIncomeDeductionQuery.Filters> filter = request.convert(WebPreviousIncomeDeductionFilters::convert);
        filter.sort("id", Direction.DESCENDING);
        filter.setWithDeleted(false);
        DataSet<PreviousIncomeDeductionVO> ds = queryService.queryPreviousIncomeDeduction(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }
    

    @Operation(summary = "应发模板下载")
    @PostMapping(value = "download/template")
    public void downloadTemplate(
            @Parameter(description = "算税方式") @RequestParam String taxCalculationMethod,
            HttpServletResponse response) {

        if (taxCalculationMethod == null) {
            throw new ApiException(ApiException.API_PARAM_ERROR, "算税方式不能为空");
        }

        // 根据算税方式选择模板路径
        String templatePath = (taxCalculationMethod.equals( EnumTaxCalculationMethod.PAYROLL_ADD.name()))
                ? SYSTEM_CALCULATION_TEMPLATE
                : PREVIOUS_INCOME_IMPORT_TEMPLATE;

        try {
            // 统一处理Excel下载
            downloadExcel(templatePath, response);
        } catch (IOException e) {
            log.error("批量导入人员模板下载失败 | 算税方式: {} | 错误信息: {}", taxCalculationMethod, e.getMessage(), e);
            throw new ApiException("批量导入人员模板下载失败", ApiException.SYSTEM_ERROR);
        }
    }

    /**
     * 通用的Excel下载方法
     */
    private void downloadExcel(String templatePath, HttpServletResponse response) throws IOException {
        try (Workbook workbook = new XSSFWorkbook(new DefaultResourceLoader().getResource(templatePath).getInputStream())) {
        String TEMPLATE_FILE_NAME = templatePath.substring(templatePath.lastIndexOf("/") + 1);
            // 设置响应头
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", CONTENT_TYPE);
            response.setHeader("Content-Disposition",CONTENT_DISPOSITION + URLEncoder.encode(TEMPLATE_FILE_NAME, "UTF-8"));

            // 写入输出流
            workbook.write(response.getOutputStream());
        }
    }

    @Operation(summary = "薪酬管理-新增工资表")
    @PostMapping(value = "addPayroll")
    @ResponseBody
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CONTRACT, spel = "#req.contractId")
    })
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_CALCULATE)
    public WebApiResponse<UploadResult> savePayroll(@Valid @ModelAttribute UploadSalaryFileRequest req) {

        ContractVo contractVo = businessContractManager.queryContract(req.getContractId());
        payrollAddRequest request = new payrollAddRequest();
        request.setCustomerId(contractVo.getCustomerId());
        request.setSupplierCorporationId(contractVo.getSupplierCorporationId());
        request.setContractId(req.getContractId());
        request.setTaxPeriod(req.getTaxPeriod());


        Long supplierId = currentSupplier().getId();
        request.setSupplierId(supplierId);
        Map<OwnerType, Set<Long>> ownerTypeSetMap = currentDataScope();
        TenantInfo tenant = currentTenant();


        //当前作业主体只能有一条非已确认状态的数据
        salaryCalculateService.verifyPayrollStaff(request);

        ExcelResult excelResult = null;
        try {
            excelResult = Excels.reader(ImportPayrollStaffRow.class)
                    .maxDataRows(5000)
                    .needVerify(true)
                    .build()
                    .read(req.getFile()[0].getInputStream());
        }  catch (Exception e) {
            log.error("批量导入人员模板下载失败 | 错误信息: {}", e.getMessage(), e);
            throw new ApiException("读取Excel文件错误", ApiException.SYSTEM_ERROR);
        }
        if (excelResult.isResultEmpty()) {
            throw new ApiException("模板中无数据,请导入数据！", ApiException.API_PARAM_ERROR);
        }
        salaryCalculateService.verifyImportPayrollStaffRow(request, excelResult.getResults(), ownerTypeSetMap);

        List<ImportPayrollStaffRow> successDataList = new ArrayList<>();
        List<PayrollCheckFailIData> failList = new ArrayList<>();

        excelResult.getResults().forEach(i -> {
            ImportPayrollStaffRow row = (ImportPayrollStaffRow) i;
            if (row.isVerifyFail()) {
                final PayrollCheckFailIData bean = BeanUtil.toBean(row, PayrollCheckFailIData.class);
                bean.setErrorMsg(row.getRowErrorString());
                failList.add(bean);
            } else {
                successDataList.add(row);
            }
        });

        UploadResult result = new UploadResult();

        if (!successDataList.isEmpty() && failList.isEmpty()) {
            salaryCalculateService.addPayrollStaff(request, successDataList, tenant);
        }else {
            String key = UUID.randomUUID().toString().replaceAll("-", "");
            redisUtils.set(key + "_payroll_fail", failList, 300);
            redisUtils.set(key + "_payroll_header", excelResult.getHeaderTitles(), 300);

            result.setUuid(key);
        }

        result.setSuccessCount(successDataList.size());
        result.setFailCount(failList.size());

        return WebApiResponse.success(result);
    }

    @Operation(summary = "错误日志下载")
    @PostMapping(value = "/importVerifyErrorLog/{uuid}")
    public void downloadErrorRecord(HttpServletRequest request, HttpServletResponse response, @PathVariable String uuid) throws IOException {
        // 获取两个错误列表
        List<ImportPayrollData> payrollFailList = redisUtils.get(uuid + "_payroll_fail", new TypeReference<>() {});
        List<ImportPreviousIncomeData> previousFailIncomeList = redisUtils.get(uuid + "_previousIncome_fail", new TypeReference<>(){});

        // 声明动态使用的数据列表和表头
        List<? extends ExcelRow> failList;

        ExcelResult excelResult;

        // 判断使用哪个数据源
        if (CollectionUtils.isNotEmpty(payrollFailList)) {
            failList = payrollFailList;
            excelResult = Excels.createWriteResult(ImportPayrollData.class);
        } else if (CollectionUtils.isNotEmpty(previousFailIncomeList)) {
            failList = previousFailIncomeList;
            excelResult = Excels.createWriteResult(ImportPreviousIncomeData.class);
        } else {
            throw new ApiException("文件已经失效，请重新上传文件进行下载！", ApiException.API_PARAM_ERROR);
        }



        failList.forEach(excelResult::addRow);

        // 生成并下载Excel文件
        final ExcelWriter excelWriter = Excels.writer(excelResult)
                .sheetName("批量导入模板")
                .build();
        final Workbook sheets = excelWriter.writeToWorkbook();

        // 设置响应头
        String fileName = String.format("错误文件_%s.xls",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        sheets.write(response.getOutputStream());
    }


    @Operation(summary = "上期收入减除导入")
    @PostMapping(value = "previousIncomeImport")
    @AuthorityDataScopGuard({
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CORPORATION, spel = "#req.supplierCorporationId")
    })
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_CALCULATE)
    public WebApiResponse<UploadResult> previousIncomeImport(@Valid @ModelAttribute UploadPreviousIncomeFileRequest req) {

        PreviousIncomeImportRequest request = new PreviousIncomeImportRequest();
        request.setSupplierCorporationId(req.getSupplierCorporationId());
        request.setTaxPeriod(req.getTaxPeriod());

        request.setSupplierId(currentSupplierId());
        TenantInfo tenant = currentTenant();
        Map<OwnerType, Set<Long>> ownerTypeSetMap = currentDataScope();

        //导入上月收入与减除时，当月工资表不能有数据，且每个月只能导入一次
        salaryCalculateService.verifyCurrentMonthPayrollStaff(request);

        ExcelResult excelResult = null;
        try {
            excelResult = Excels.reader(ImportPreviousIncomeRow.class)
                    .maxDataRows(5000)
                    .needVerify(true)
                    .build()
                    .read(req.getFile()[0].getInputStream());
        }  catch (Exception e) {
            throw new ApiException("读取Excel文件错误", ApiException.SYSTEM_ERROR);
        }
        if (excelResult.isResultEmpty()) {
            throw new ApiException("模板中无数据,请导入数据！", ApiException.API_PARAM_ERROR);
        }

        salaryCalculateService.verifyPreviousIncomeInfo(request, excelResult.getResults(), ownerTypeSetMap);
        List<ImportPreviousIncomeRow> successDataList = new ArrayList<>();
        List<PreviousIncomeImportFailData> failList = new ArrayList<>();

        excelResult.getResults().forEach(i -> {
            ImportPreviousIncomeRow row = (ImportPreviousIncomeRow) i;
            if (row.isVerifyFail()) {
                PreviousIncomeImportFailData failData = BeanUtil.toBean(row, PreviousIncomeImportFailData.class);
                failData.setErrorMsg(row.getRowErrorString());
                failList.add(failData);
            } else {
                successDataList.add(row);
            }
        });

        UploadResult result = new UploadResult();

        if (!successDataList.isEmpty() && failList.isEmpty()) {
            salaryCalculateService.addPreviousIncomeStaff(request, successDataList, tenant);
        } else {
            String key = UUID.randomUUID().toString().replaceAll("-", "");
            redisUtils.set(key + "_previousIncome_fail", failList, 300);
            redisUtils.set(key + "_previousIncome_header", excelResult.getHeaderTitles(), 300);
            result.setUuid(key);
        }

        result.setSuccessCount(successDataList.size());
        result.setFailCount(failList.size());

        return WebApiResponse.success(result);
    }

    @Data
    @Schema(description = "上传发薪信息")
    public static class UploadSalaryFileRequest {

        @ValidMultipartFileArray(
                maxCount = 1,
                maxSizeInBytes = 5 * 1024 * 1024, // 最大5MB
                allowedTypes = {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"}
        )
        @NotNull(message = "请上传文件")
        @Schema(description = "文件")
        private MultipartFile[] file;

        @Schema(description = "合同ID")
        @NotNull(message = "服务合同不能为空")
        private Long contractId;

        @Schema(description = "税款所属期")
        @NotNull(message = "税款所属期不能为空")
        private String taxPeriod;
    }

    @Data
    @Schema(description = "上传上期收入减除信息")
    public static class UploadPreviousIncomeFileRequest {

        @ValidMultipartFileArray(
                maxCount = 1,
                maxSizeInBytes = 5 * 1024 * 1024, // 最大5MB
                allowedTypes = {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"}
        )
        @NotNull(message = "请上传文件")
        @Schema(description = "文件")
        private MultipartFile[] file;

        @Schema(description = "主体ID")
        @NotNull(message = "主体不能为空")
        private Long supplierCorporationId;

        @Schema(description = "税款所属期")
        @NotNull(message = "税款所属期不能为空")
        private String taxPeriod;
    }


    @Data
    public static class WebSalaryFilters {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "合同id")
        private Long contractId;

        @Schema(description = "合同名称")
        private String contractName;

        @Schema(description = "客户id")
        @JsonDeserialize(using = NullIfEmptyInSetDeserializer.class)
        private Set<Long> customerId;

        @Schema(description = "客户名称")
        private String customerName;

        @Schema(description = "作业主体id")
        private Long supplierCorporationId;

        @Schema(description = "作业主体名称")
        private String supplierCorporationName;

        @Schema(description = "工资表状态")
        private String status;

        @Schema(description = "服务商id",hidden = true)
        private Long supplierId;

        @Schema(description = "服务合同列表",hidden = true)
        private Set<Long> contractIds;


        public SalaryQuery.Filters convert() {
            return Beans.copyBean(this, SalaryQuery.Filters.class);
        }
    }

    @Data
    public static class WebPreviousIncomeDeductionFilters {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "作业主体id")
        private Long supplierCorporationId;

        @Schema(description = "作业主体id列表",hidden = true)
        private Set<Long> supplierCorporationIds;

        @Schema(description = "作业主体名称")
        private String supplierCorporationName;

        @Schema(description = "姓名")
        private String fullName;

        @Schema(description = "税款所属期")
        @Pattern(
                regexp = "^(19|20)\\d{2}-(0[1-9]|1[0-2])$",
                message = "税款所属期格式必须为 yyyy-MM（如 2023-05）"
        )
        private String taxPeriod;

        public PreviousIncomeDeductionQuery.Filters convert() {
            return Beans.copyBean(this, PreviousIncomeDeductionQuery.Filters.class);
        }
    }

    @Data
    public static class SalaryParams {

        @NotNull(message = "工资表ID不能为空")
        @Schema(description = "工资表ID")
        private Long id;

    }


    @Getter
    @Setter
    public static class UploadResult {
        @Schema(description = "成功条数")
        private Integer successCount;
        @Schema(description = "失败条数")
        private Integer failCount;
        @Schema(description = "key")
        private String uuid;
    }

    @Data
    public static class payrollAddRequest {

        @Schema(description = "客户ID")
        @NotNull(message = "客户不能为空")
        private Long customerId;

        @Schema(description = "作业主体ID")
        @NotNull(message = "作业主体不能为空")
        private Long supplierCorporationId;

        @Schema(description = "合同ID")
        @NotNull(message = "服务合同不能为空")
        private Long contractId;

        @Schema(description = "税款所属期")
        @NotBlank(message = "税款所属期不能为空")
        @Pattern(
                regexp = "^\\d{4}-(0[1-9]|1[0-2])$",  // yyyy-MM 格式正则
                message = "税款所属期格式必须为 yyyy-MM（如 2023-05）"
        )
        private String taxPeriod;

        @Schema(description = "申报月")
        private String reportMonth;

        @Schema(description = "服务商ID")
        private Long supplierId;


        @Schema(description = "算税方式")
        private EnumTaxCalculationMethod taxCalculationMethod;
    }


}
