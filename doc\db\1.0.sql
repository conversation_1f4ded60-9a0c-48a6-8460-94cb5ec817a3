drop database olading_labor;
CREATE DATABASE olading_labor;
use olading_labor;

create table if not exists t_bill_category
(
    id               bigint auto_increment comment '主键ID'
        primary key,
    bill_master_id   bigint                                      not null comment '账单主表ID',
    fee_type         varchar(20)                                 not null comment '费用类型：SALARY-薪酬,MANAGEMENT_FEE-管理费,OTHER_FEE-其他费用',
    total_amount     decimal(19, 2) default 0.00                 not null comment '费用总额',
    detail_count     int            default 0                    not null comment '明细条数',
    person_count     int            default 0                    not null comment '涉及人数',
    calculation_rule varchar(200)                                null comment '计算规则说明',
    bill_month       date                                        not null comment '账单月份',
    remark           varchar(500)                                null comment '备注',
    tenant_id        varchar(20)                                 null comment '租户编号',
    create_time      datetime(6)    default CURRENT_TIMESTAMP(6) null comment '创建时间',
    modify_time      datetime(6)    default CURRENT_TIMESTAMP(6) null on update CURRENT_TIMESTAMP(6) comment '修改时间',
    deleted          bit            default b'0'                 null comment '是否删除',
    deleted_time     datetime(6)                                 null comment '删除时间',
    version          int            default 0                    not null comment '版本号',
    constraint uk_bill_fee_type
        unique (bill_master_id, fee_type)
)
    comment '账单分类统计表';

create index idx_bill_master
    on t_bill_category (bill_master_id);
create index idx_fee_type
    on t_bill_category (fee_type);
CREATE INDEX idx_bill_cat_month_tenant ON t_bill_category (bill_month, tenant_id);

create table if not exists t_bill_management_fee_detail
(
    id                    bigint auto_increment comment '主键ID'
        primary key,
    bill_master_id        bigint                                      not null comment '账单主表ID',
    bill_category_id      bigint                                      not null comment '账单分类ID',
    labor_name            varchar(50)                                 not null comment '人员姓名',
    id_card               varchar(18)                                 not null comment '身份证号',
    fee_item              varchar(100)                                not null comment '收费项目',
    management_fee_amount decimal(19, 2) default 0.00                 not null comment '管理费金额',
    bill_month            date                                        not null comment '账单月份',
    calculation_base      decimal(19, 2)                              null comment '计算基数',
    calculation_rate      decimal(8, 4)                               null comment '计算费率',
    calculation_rule      varchar(50)                                 null comment '计算规则',
    remark                varchar(200)                                null comment '备注',
    tenant_id             varchar(20)                                 null comment '租户编号',
    create_time           datetime(6)    default CURRENT_TIMESTAMP(6) null comment '创建时间',
    modify_time           datetime(6)    default CURRENT_TIMESTAMP(6) null on update CURRENT_TIMESTAMP(6) comment '修改时间',
    deleted               bit            default b'0'                 null comment '是否删除',
    deleted_time          datetime(6)                                 null comment '删除时间',
    version               int            default 0                    not null comment '版本号'
)
    comment '账单管理费明细表';

create index idx_bill_category on t_bill_management_fee_detail (bill_category_id);
create index idx_bill_master on t_bill_management_fee_detail (bill_master_id);
create index idx_bill_month on t_bill_management_fee_detail (bill_month);
create index idx_fee_item on t_bill_management_fee_detail (fee_item);
create index idx_id_card on t_bill_management_fee_detail (id_card);
CREATE INDEX idx_mgmt_fee_mst_cat ON t_bill_management_fee_detail (bill_master_id, bill_category_id);
CREATE INDEX idx_mgmt_fee_id_month ON t_bill_management_fee_detail (id_card, bill_month);




create table if not exists t_bill_master
(
    id                      bigint auto_increment comment '主键ID'
        primary key,
    bill_no                 varchar(64)                                 not null comment '账单编号',
    supplier_id             bigint                                      not null comment '供应商ID',
    customer_id             bigint                                      not null comment '客户ID',
    supplier_corporation_id bigint                                      not null comment '作业主体ID',
    contract_id             bigint                                      not null comment '客户合同ID',
    bill_month              date                                        not null comment '账单月份',
    total_receivable_amount decimal(19, 2) default 0.00                 not null comment '账单应收总费用',
    salary_amount           decimal(19, 2) default 0.00                 not null comment '薪酬费用总额',
    management_fee_amount   decimal(19, 2) default 0.00                 not null comment '管理费总额',
    other_fee_amount        decimal(19, 2) default 0.00                 not null comment '其他费用总额',
    total_invoice_amount    decimal(19, 2) default 0.00                 not null comment '开票总金额',
    invoiced_amount         decimal(19, 2) default 0.00                 not null comment '已开票金额',
    received_amount         decimal(19, 2) default 0.00                 not null comment '已收款金额',
    bill_status             varchar(20)    default 'DRAFT'              not null comment '账单状态',
    confirm_time            datetime                                    null comment '确认时间',
    confirm_user_id         bigint                                      null comment '确认人ID',
    remark                  varchar(500)                                null comment '备注',
    tenant_id               varchar(20)                                 null comment '租户编号',
    create_time             datetime(6)    default CURRENT_TIMESTAMP(6) null comment '创建时间',
    modify_time             datetime(6)    default CURRENT_TIMESTAMP(6) null on update CURRENT_TIMESTAMP(6) comment '修改时间',
    deleted                 bit            default b'0'                 null comment '是否删除',
    deleted_time            datetime(6)                                 null comment '删除时间',
    version                 int            default 0                    not null comment '版本号',
    constraint bill_no
        unique (bill_no)
)
    comment '账单主表';

create index idx_bill_status
    on t_bill_master (bill_status);
create index idx_contract_month
    on t_bill_master (contract_id, bill_month);
create index idx_create_time
    on t_bill_master (create_time);
create index idx_supplier_customer
    on t_bill_master (supplier_id, customer_id);
CREATE INDEX idx_bill_mst_sup_cust_cont ON t_bill_master (supplier_id, customer_id, contract_id);
CREATE INDEX idx_bill_mst_corp_month ON t_bill_master (supplier_corporation_id, bill_month);
CREATE INDEX idx_bill_mst_month ON t_bill_master (bill_month);
CREATE INDEX idx_bill_gen_complex ON t_bill_master (supplier_id, customer_id, contract_id, bill_month);

create table if not exists t_bill_other_fee_detail
(
    id               bigint auto_increment comment '主键ID'
        primary key,
    bill_master_id   bigint                                      not null comment '账单主表ID',
    bill_category_id bigint                                      not null comment '账单分类ID',
    labor_name       varchar(50)                                 not null comment '人员姓名',
    id_card          varchar(18)                                 not null comment '身份证号',
    occur_date       date                                        null comment '产生时间',
    fee_name         varchar(100)                                not null comment '费用名称',
    fee_amount       decimal(19, 2) default 0.00                 not null comment '费用金额',
    fee_purpose      varchar(200)                                not null comment '费用用途',
    bill_month       date                                        not null comment '账单月份',
    fee_category     varchar(50)                                 null comment '费用类别',
    fee_description  varchar(500)                                null comment '费用说明',
    import_batch_no  varchar(50)                                 null comment '导入批次号',
    remark           varchar(200)                                null comment '备注',
    tenant_id        varchar(20)                                 null comment '租户编号',
    create_time      datetime(6)    default CURRENT_TIMESTAMP(6) null comment '创建时间',
    modify_time      datetime(6)    default CURRENT_TIMESTAMP(6) null on update CURRENT_TIMESTAMP(6) comment '修改时间',
    deleted          bit            default b'0'                 null comment '是否删除',
    deleted_time     datetime(6)                                 null comment '删除时间',
    version          int            default 0                    not null comment '版本号'
)
    comment '账单其他费用明细表';

create index idx_bill_category
    on t_bill_other_fee_detail (bill_category_id);
create index idx_bill_master
    on t_bill_other_fee_detail (bill_master_id);
create index idx_bill_month
    on t_bill_other_fee_detail (bill_month);
create index idx_fee_name
    on t_bill_other_fee_detail (fee_name);
create index idx_id_card
    on t_bill_other_fee_detail (id_card);
create index idx_import_batch
    on t_bill_other_fee_detail (import_batch_no);
CREATE INDEX idx_other_fee_mst_cat ON t_bill_other_fee_detail (bill_master_id, bill_category_id);
CREATE INDEX idx_other_fee_id_month ON t_bill_other_fee_detail (id_card, bill_month);


create table if not exists t_bill_salary_detail
(
    id               bigint auto_increment comment '主键ID'
        primary key,
    bill_master_id   bigint                                      not null comment '账单主表ID',
    bill_category_id bigint                                      not null comment '账单分类ID',
    salary_detail_id bigint                                      not null comment '薪酬明细ID',
    salary_batch_id  bigint                                      not null comment '薪酬批次ID',
    labor_name       varchar(50)                                 not null comment '人员姓名',
    id_card          varchar(18)                                 not null comment '身份证号',
    gross_salary     decimal(19, 2) default 0.00                 not null comment '应发工资',
    net_salary       decimal(19, 2) default 0.00                 not null comment '实发工资',
    income_tax       decimal(19, 2) default 0.00                 not null comment '应缴个税',
    vat_tax          decimal(19, 2) default 0.00                 not null comment '应缴增值税',
    additional_tax   decimal(19, 2) default 0.00                 not null comment '应缴附加税',
    bill_month       date                                        not null comment '账单月份',
    salary_period    varchar(20)                                 null comment '工资所属期',
    remark           varchar(200)                                null comment '备注',
    tenant_id        varchar(20)                                 null comment '租户编号',
    create_time      datetime(6)    default CURRENT_TIMESTAMP(6) null comment '创建时间',
    modify_time      datetime(6)    default CURRENT_TIMESTAMP(6) null on update CURRENT_TIMESTAMP(6) comment '修改时间',
    deleted          bit            default b'0'                 null comment '是否删除',
    deleted_time     datetime(6)                                 null comment '删除时间',
    version          int            default 0                    not null comment '版本号',
    constraint uk_salary_detail
        unique (bill_master_id, salary_detail_id)
)
    comment '账单薪酬明细表';

create index idx_bill_category
    on t_bill_salary_detail (bill_category_id);
create index idx_bill_master
    on t_bill_salary_detail (bill_master_id);
create index idx_bill_month
    on t_bill_salary_detail (bill_month);
create index idx_id_card
    on t_bill_salary_detail (id_card);
create index idx_salary_batch
    on t_bill_salary_detail (salary_batch_id);
CREATE INDEX idx_bill_sal_mst_cat ON t_bill_salary_detail (bill_master_id, bill_category_id);
CREATE INDEX idx_bill_sal_id_month ON t_bill_salary_detail (id_card, bill_month);



create table if not exists t_business_contract
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)   null comment '创建时间',
    deleted                 bit           null comment '是否已删除',
    deleted_time            datetime(6)   null comment '删除时间',
    modify_time             datetime(6)   null comment '更新时间',
    tenant_id               varchar(20)   null comment '租户编号',
    version                 int           not null comment '版本',
    supplier_id             bigint        null comment '灵工平台id',
    customer_id             bigint        not null comment '客户id',
    supplier_corporation_id bigint        not null comment '作业主体id',
    name                    varchar(150)  not null comment '合同名称',
    status                  varchar(20)   not null comment '状态',
    sn                      varchar(64)   not null comment '编号',
    time_fixed              tinyint(1)    not null comment '合同期限是否固定',
    start_date              date          null comment '开始日期',
    end_date                date          null comment '结束日期',
    stopped                 tinyint(1)    null comment '是否已提前中止:0,1',
    stop_time               datetime      null comment '提前中止时间',
    stop_reason             varchar(500)  null comment '提前中止原因',
    business_type           varchar(16)   null comment '业务类型',
    remark                  varchar(500)  null comment '备注',
    file_ids                varchar(5000) null comment '附件id,逗号分隔',
    pre_contract_id         bigint        null comment '原合同id',
    rear_contract_id        bigint        null comment '续签合同id',
    creator_id              bigint        null comment '创建者id',
    updater_id              bigint        null comment '更新者id',
    constraint idx_su_name
        unique (supplier_id, name)
)
    comment '客户服务合同';
CREATE INDEX idx_cont_sup_cust ON t_business_contract (supplier_id, customer_id);
CREATE INDEX idx_cont_corp ON t_business_contract (supplier_corporation_id);
CREATE INDEX idx_cont_status ON t_business_contract (status);
CREATE INDEX idx_cont_name_sup ON t_business_contract (name, supplier_id);
CREATE INDEX idx_cont_dates ON t_business_contract (start_date, end_date);
CREATE INDEX idx_cont_supplier ON t_business_contract (supplier_id);
CREATE INDEX idx_cont_customer ON t_business_contract (customer_id);
CREATE INDEX idx_cont_tenant ON t_business_contract (tenant_id);

create table if not exists t_business_contract_config
(
    id                       bigint auto_increment
        primary key,
    create_time              datetime(6)    null comment '创建时间',
    deleted                  bit            null comment '是否已删除',
    deleted_time             datetime(6)    null comment '删除时间',
    modify_time              datetime(6)    null comment '更新时间',
    tenant_id                varchar(20)    null comment '租户编号',
    version                  int            not null comment '版本',
    customer_id              bigint         not null comment '客户id',
    supplier_corporation_id  bigint         not null comment '作业主体id',
    contract_id              bigint         not null comment '合同名称',
    invoice_title            varchar(64)    null comment '抬头',
    invoice_tax_no           varchar(64)    null comment '纳税识别号',
    invoice_bank_name        varchar(64)    null comment '开户行',
    invoice_bank_account     varchar(64)    null comment '账号',
    invoice_register_address varchar(64)    null comment '注册地址',
    invoice_company_tel      varchar(64)    null comment '企业电话',
    invoice_remark           varchar(500)   null comment '发票备注',
    manage_calculation_rule  varchar(20)    null comment '计算规则',
    manage_amount            decimal(16, 2) null comment '金额',
    manage_rate              decimal(16, 2) null comment '费率'
)
    comment '服务合同配置';
CREATE INDEX idx_cont_cfg_cust_corp ON t_business_contract_config (customer_id, supplier_corporation_id);
CREATE INDEX idx_cont_cfg_contract ON t_business_contract_config (contract_id);


create table if not exists t_channel_remit_order
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)    null comment '创建时间',
    deleted                 bit            null comment '是否已删除',
    deleted_time            datetime(6)    null comment '删除时间',
    modify_time             datetime(6)    null comment '更新时间',
    tenant_id               varchar(20)    null comment '租户编号',
    version                 int            not null comment '版本',
    supplier_id             bigint         not null comment '灵工平台id',
    supplier_corporation_id bigint         not null comment '作业主体ID',
    proxy_order_id          bigint         not null comment '工资代发订单id',
    pay_channel             varchar(64)    not null comment '通道编码',
    request_no              varchar(64)    not null comment '出款流水号',
    status                  varchar(20)    not null comment '状态',
    name                    varchar(20)    not null comment '姓名',
    id_card                 varchar(20)    not null comment '身份证号',
    cellphone               varchar(11)    not null comment '手机号',
    bank_card               varchar(24)    not null comment '银行卡号',
    bank_code               varchar(20)    null comment '出款银行编码',
    bank_name               varchar(20)    null comment '出款银行名称',
    amount                  decimal(38, 2) not null comment '付款金额',
    error_code              varchar(64)    null comment '错误码',
    error_reason            varchar(100)   null comment '错误原因',
    finish_time             datetime(6)    null comment '完成时间',
    voucher                 varchar(50)    null comment '凭证文件id',
    remark                  varchar(200)   null comment '业务备注'
)
    comment '通道出款订单';
CREATE INDEX idx_channel_remit_order_supplier_id ON t_channel_remit_order (supplier_id);
CREATE INDEX idx_channel_remit_order_supplier_corporation_id ON t_channel_remit_order (supplier_corporation_id);
CREATE INDEX idx_channel_remit_order_proxy_order_id ON t_channel_remit_order (proxy_order_id);
CREATE INDEX idx_channel_remit_order_pay_channel ON t_channel_remit_order (pay_channel);
CREATE INDEX idx_channel_remit_order_request_no ON t_channel_remit_order (request_no);
CREATE INDEX idx_channel_remit_order_status ON t_channel_remit_order (status);
CREATE INDEX idx_channel_remit_order_cre_sta ON t_channel_remit_order (create_time,status);

create table if not exists t_corporation_config
(
    id                      bigint auto_increment comment 'id'
        primary key,
    create_time             datetime(6)    null comment '创建时间',
    deleted                 bit            null comment '是否已删除',
    deleted_time            datetime(6)    null comment '删除时间',
    modify_time             datetime(6)    null comment '更新时间',
    tenant_id               varchar(20)    null comment '租户编号',
    version                 int            not null comment '版本',
    supplier_id             bigint         not null comment '灵工平台id',
    supplier_corporation_id bigint         not null comment '作业主体ID',
    min_age_limit           int            null comment '最小年龄限制',
    max_age_limit           int            null comment '最大年龄限制',
    invoice_category        text           null comment '发票类目配置(json)',
    vat_start               decimal(19, 2) null comment '增值税起征点(万元)',
    vat_rate                decimal(19, 2) null comment '增值税税率(%)',
    surtax_data             text           null comment '附加税配置(json)'
)
    comment '作业主体配置';
CREATE INDEX idx_corp_cfg_supplier_id ON t_corporation_config (supplier_id);
create unique index udx_corp_id on t_corporation_config (supplier_corporation_id);

create table if not exists t_corporation_pay_channel
(
    id                      bigint auto_increment comment 'id'
        primary key,
    create_time             datetime(6)      null comment '创建时间',
    deleted                 bit              null comment '是否已删除',
    deleted_time            datetime(6)      null comment '删除时间',
    modify_time             datetime(6)      null comment '更新时间',
    tenant_id               varchar(20)      null comment '租户编号',
    version                 int              not null comment '版本',
    supplier_id             bigint           not null comment '灵工平台id',
    supplier_corporation_id bigint           not null comment '作业主体ID',
    pay_channel             varchar(64)      not null comment '通道编码',
    is_default              bit default b'0' not null comment '通道名称',
    is_open                 bit default b'1' not null comment '是否启用',
    channel_config          text             null comment '通道配置信息'
)
    comment '作业主体通道配置信息';
CREATE INDEX idx_corp_pay_channel_supplier_id ON t_corporation_pay_channel (supplier_id);
create unique index udx_corp_pay_channel ON t_corporation_pay_channel (supplier_corporation_id, pay_channel);

create table if not exists t_corporation_protocol
(
    id                      bigint auto_increment comment 'id'
        primary key,
    supplier_id             bigint           null comment '平台id',
    supplier_corporation_id bigint           null comment '作业主体id',
    tenant_id               varchar(20)      null comment '租户',
    version                 int              null comment 'version',
    protocol_name           varchar(60)      null comment '协议名',
    id_card                 varchar(20)      null comment '签约证件号',
    template_id             bigint           null comment '模板id',
    customer_id             bigint           null comment '客户id',
    contract_id             bigint           null comment '服务合同id',
    start_date              date             null comment '合同开始日期',
    end_date                date             null comment '合同结束日期',
    silence_sign            char             null comment '企业静默签',
    file_id                 varchar(32)      null comment '文档id 要签署的文档id（已填入模板域）',
    protocol_file_id        varchar(32)      null comment '合同文档id，有盖章的文档',
    protocol_type           varchar(30)      null comment '协议类型',
    sign_status             varchar(20)      null comment '签署状态',
    create_date             date             null comment '创建日期',
    finish_date             date             null comment '签署完成日期',
    agent_template          varchar(30)      null comment '云签模板编号',
    agent_contract_id       bigint           null comment '云签文件编号',
    create_user_id          bigint           null comment '创建人员id',
    handle_info             varchar(200)     null comment '拒签理由',
    upload_url              varchar(1024)    null comment '文档上传url',
    edit_url                varchar(1024)    null comment '文档编辑url',
    create_time             datetime         null comment '创建时间',
    modify_time             datetime         null comment '更新时间',
    deleted                 bit default b'0' null,
    deleted_time            datetime         null comment '删除时间'
)
    comment '协议信息表';
CREATE INDEX idx_corp_prot_sup_corp ON t_corporation_protocol (supplier_id, supplier_corporation_id);
CREATE INDEX idx_corp_prot_id_status ON t_corporation_protocol (id_card, sign_status);
CREATE INDEX idx_corp_prot_cust_cont ON t_corporation_protocol (customer_id, contract_id);
CREATE INDEX idx_corp_prot_template ON t_corporation_protocol (template_id);
CREATE INDEX idx_corp_prot_dates ON t_corporation_protocol (start_date, end_date);
CREATE INDEX idx_corp_prot_supplier ON t_corporation_protocol (supplier_id);
CREATE INDEX idx_corp_prot_corp ON t_corporation_protocol (supplier_corporation_id);
CREATE INDEX idx_corp_idcard ON t_corporation_protocol (supplier_corporation_id, id_card);

create table if not exists t_corporation_protocol_filed
(
    id                      bigint auto_increment comment 'id'
        primary key,
    supplier_id             bigint      null comment '平台id',
    tenant_id               varchar(20) null comment '租户',
    protocol_id             bigint      null comment '合同id',
    step_id                 bigint      null comment '步骤id',
    supplier_corporation_id bigint      null comment '作业主体id',
    field_name              varchar(32) null comment '字段名',
    relation_code           varchar(32) null comment '字段编码',
    field_value             varchar(32) null comment '字段值',
    version                 int         null comment 'version',
    create_time             datetime    null comment '创建时间',
    modify_time             datetime    null comment '更新时间'
) comment '协议字段信息表';

CREATE INDEX idx_corp_prot_filed_prot_id ON t_corporation_protocol_filed (protocol_id);
CREATE INDEX idx_corp_prot_filed_step_id ON t_corporation_protocol_filed (step_id);

create table if not exists t_corporation_protocol_step
(
    id                      bigint auto_increment comment 'id'
        primary key,
    supplier_id             bigint           null comment '平台id',
    tenant_id               varchar(20)      null comment '租户',
    protocol_id             bigint           null comment '合同id',
    operate                 varchar(32)      null comment '操作类型  公章签署、个人签署',
    sortby                  int              null comment '步骤顺序',
    id_card                 varchar(18)      null comment '证件号码',
    supplier_corporation_id bigint           null comment '作业主体id',
    step_name               varchar(32)      null comment '步骤名称',
    flow_no                 varchar(32)      null comment '请求云签签名流水号',
    sign_image              varchar(32)      null comment '签署图片',
    sign_status             varchar(32)      null comment '签署状态',
    sign_url                varchar(128)     null comment 'pc端签署url',
    sign_url_mobile         varchar(128)     null comment '移动端签署url',
    sign_file_id            varchar(32)      null comment '签名文件文档服务id 步骤签署完成后查询文件，存储到文件服务',
    sign_time               datetime         null comment '签署时间',
    current_step            bit default b'0' null comment '是否为当前步骤',
    received                bit default b'0' null comment '是否已查看',
    received_time           datetime         null comment '查看时间',
    file_reason             varchar(500)     null comment '签署失败原因',
    version                 int              null comment 'version',
    create_time             datetime         null comment '创建时间',
    update_time             datetime         null comment '更新时间'
) comment '协议步骤信息表';

create INDEX idx_corp_prot_step_prot_id ON t_corporation_protocol_step (protocol_id);
CREATE INDEX idx_corp_id ON t_corporation_protocol_step (supplier_corporation_id);

create table if not exists t_corporation_protocol_temp_filed
(
    id                 bigint auto_increment
        primary key,
    tenant_id          varchar(20)   not null comment '终端id',
    version            int           null,
    supplier_id        bigint        null comment '平台id',
    template_id        bigint        not null comment '协议模板id',
    template_step_id   bigint        not null comment '模板步骤id',
    template_step_name varchar(32)   null comment '模板步骤名',
    field_name         varchar(32)   null comment '字段名',
    default_value      varchar(1024) null,
    relation_code      varchar(32)   null comment '关联项',
    relation_name      varchar(128)  null comment '关联项名称',
    relation_group     varchar(32)   null comment '关联项所属分组',
    create_time        datetime      null comment '创建时间',
    modify_time        datetime      null comment '更新时间',
    operate            varchar(32)   null comment '域类型 SEAL-企业签章 SIGN-个人签章 DATE-日期 FIELD-填充域',
    field_code         varchar(32)   null comment '域编码',
    signatory          varchar(32)   null comment '签署方(1-企业签署方 2-个人签署方)'
)
    comment '协议模板域信息';
create index idx_corp_temp_id on t_corporation_protocol_temp_filed (template_id);

create table if not exists t_corporation_protocol_temp_range
(
    id                      bigint auto_increment comment 'id'
        primary key,
    tenant_id               int      null comment '租户id',
    supplier_id             bigint   null comment '平台id',
    template_id             bigint   null comment '模板id',
    range_type              int      null comment '模板适用范围类型',
    supplier_corporation_id bigint   null comment '作业主体id',
    sorted_by               int      null comment '排序',
    version                 int      null comment 'version',
    create_time             datetime null comment '创建时间',
    modify_time             datetime null comment '更新时间'
)
    comment '模板适用范围表';
create index idx_corp_temp_range_id on t_corporation_protocol_temp_range (template_id);
create index idx_corp_id on t_corporation_protocol_temp_range (supplier_corporation_id);

create table if not exists t_corporation_protocol_temp_step
(
    id               bigint auto_increment comment 'id'
        primary key,
    tenant_id        varchar(20) null comment '终端id',
    version          int         null,
    supplier_id      bigint      null comment '平台id',
    supplier_step_id bigint      null comment '作业主体id',
    template_id      bigint      null comment '协议模板id',
    operate          varchar(32) null comment '操作类型 公章签署、个人签署、抄送',
    sort_by          int         null comment '步骤排序',
    step_name        varchar(32) null comment '步骤名',
    create_time      datetime    null comment '创建时间',
    modify_time      datetime    null comment '更新时间'
) comment '协议模板步骤信息表';
create index idx_corp_temp_step_id on t_corporation_protocol_temp_step (template_id);
create index idx_corp_step_id on t_corporation_protocol_temp_step (supplier_step_id);

create table if not exists t_corporation_protocol_template
(
    id               bigint auto_increment comment 'id'
        primary key,
    create_time      datetime(6)      null comment '创建时间',
    deleted          bit default b'0' null comment '是否已删除',
    deleted_time     datetime(6)      null comment '删除时间',
    modify_time      datetime(6)      null comment '更新时间',
    tenant_id        varchar(20)      null comment '租户编号',
    version          int default 0    not null comment '版本',
    supplier_id      bigint           not null comment '灵工平台id',
    temp_name        varchar(64)      null comment '模板名称',
    temp_type        varchar(30)      null comment '协议类型',
    agent_template   varchar(30)      null comment '协议模版id',
    template_file_id varchar(32)      null comment '模板文件id',
    edit_url         varchar(1024)    null comment '模板编辑url',
    upload_url       varchar(1024)    null comment '模板上传url',
    status           varchar(20)      null comment '协议可用状态'
)
    comment '作业主体协议模版配置信息';
create index idx_corp_temp_id on t_corporation_protocol_template (supplier_id);
create index idx_corp_agent_temp_id on t_corporation_protocol_template (agent_template);


create table if not exists t_customer
(
    id                 bigint auto_increment
        primary key,
    create_time        datetime(6)  null comment '创建时间',
    deleted            bit          null comment '是否已删除',
    deleted_time       datetime(6)  null comment '删除时间',
    modify_time        datetime(6)  null comment '更新时间',
    tenant_id          varchar(20)  null comment '租户编号',
    version            int          null comment '版本',
    supplier_id        bigint       null comment '灵工平台id',
    name               varchar(200) not null comment '客户名称',
    enterprise_info_id bigint       null comment '企业信息ID',
    sn                 varchar(64)  null comment '编号',
    short_name         varchar(64)  null comment '简称',
    status             varchar(20)  null comment '状态:1-未合作;2-合作中;3-停止合作',
    region_id          varchar(64)  null comment '地区id',
    address            varchar(160) null comment '详细地址',
    industry           varchar(20)  null comment '行业',
    type               varchar(20)  null comment '性质',
    size               varchar(20)  null comment '规模',
    source             varchar(64)  null comment '来源',
    sales_name         varchar(20)  null comment '销售负责人姓名',
    service_mobile     varchar(20)  null comment '销售负责人电信',
    contact_name       varchar(64)  null comment '客户联系人姓名',
    contact_mobile     varchar(64)  null comment '客户联系人电话',
    remark             varchar(200) null comment '备注',
    disabled           bit          null comment '是否禁用',
    user_id            bigint       null comment '用户ID'
)
    comment '客户信息表';
CREATE INDEX idx_cust_supplier ON t_customer (supplier_id);
CREATE unique INDEX idx_cust_su_name ON t_customer (supplier_id,name);
CREATE INDEX idx_cust_create_time ON t_customer (create_time);
CREATE INDEX idx_cust_ent_info ON t_customer (enterprise_info_id);


create table if not exists t_enterprise_info
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)   null comment '创建时间',
    deleted                 bit           null comment '是否已删除',
    deleted_time            datetime(6)   null comment '删除时间',
    modify_time             datetime(6)   null comment '更新时间',
    tenant_id               varchar(20)   null comment '租户编号',
    version                 int           not null comment '版本',
    contact_phone           varchar(100)  null comment '联系电话',
    contacts                varchar(100)  null comment '联系人',
    name                    varchar(100)  null comment '名称',
    owner_id                bigint        null comment '实体ID',
    owner_type              varchar(20)   null comment '实体类型 SUPPLIER,CORPORATION,CUSTOMER',
    attachments             longtext      null comment '附件文件ID列表',
    remark                  varchar(1024) null comment '备注',
    business_license_image  varchar(64)   null comment '营业执照图片id',
    social_credit_code      varchar(64)   null comment '统一社会信用代码',
    representative_name     varchar(64)   null comment '法定代表人姓名',
    certificate_type        tinyint       null comment '法定代表人证件类型:1-身份证;2-护照;3-港澳台;4-外国人',
    certificate_no          varchar(64)   null comment '法定代表人证件号',
    certificate_front_image varchar(64)   null comment '法定代表人证件正面照',
    certificate_back_image  varchar(64)   null comment '法定代表人证件背面照',
    registered_address      varchar(64)   null comment '公司注册地址',
    constraint i_enterprise_info_1
        unique (owner_id, owner_type)
)
    comment '企业信息';
CREATE INDEX idx_ent_info_credit ON t_enterprise_info (social_credit_code);

create table if not exists t_file
(
    id           varchar(32)  not null
        primary key,
    create_time  datetime(6)  null comment '创建时间',
    deleted      bit          null comment '是否已删除',
    deleted_time datetime(6)  null comment '删除时间',
    modify_time  datetime(6)  null comment '更新时间',
    tenant_id    varchar(20)  null comment '租户编号',
    version      int          not null comment '版本',
    expiry_time  datetime(6)  null comment '文件过期时间',
    name         varchar(200) null comment '文件名',
    owner_id     bigint       null comment '实体ID',
    owner_type   varchar(20)  null comment '实体类型',
    storage_id   varchar(100) null
)
    comment '文件表';

create index idx_file
    on t_file (owner_id, owner_type);
create index idx_file_sid on t_file (storage_id);

create table if not exists t_identity_face_record
(
    id                 bigint auto_increment comment '主键ID'
        primary key,
    tenant_id          varchar(20)          not null comment '租户ID',
    record_no          varchar(64)          not null comment '认证记录编号',
    user_id            bigint               null comment '用户ID（可空，兼容非登录场景）',
    supplier_id        bigint               null comment '供应商ID',
    corporation_id     bigint               null comment '作业主体ID',
    name               varchar(100)         not null comment '真实姓名',
    id_card            varchar(18)          not null comment '身份证号',
    face_video_file_id varchar(32)          null comment '人脸视频文件ID',
    similarity_score   decimal(5, 4)        null comment '相似度分数',
    liveness_score     decimal(5, 4)        null comment '活体检测分数',
    auth_scene         varchar(30)          not null comment '认证场景：CONTRACT_SIGNING,PAYMENT_DISBURSEMENT,REAL_NAME_AUTH',
    face_status        varchar(20)          not null comment '人脸识别状态：SUCCESS,FAILED',
    face_result        varchar(20)          null comment '识别结果：PASSED,REJECTED',
    error_code         varchar(50)          null comment '错误码',
    error_message      varchar(500)         null comment '错误信息',
    create_time        datetime             not null comment '创建时间',
    modify_time        datetime             not null comment '修改时间',
    deleted            tinyint(1) default 0 null comment '是否删除',
    deleted_time       datetime             null comment '删除时间',
    version            int        default 0 null comment '版本号'
)
    comment '人脸识别记录表';
CREATE INDEX idx_face_rec_user ON t_identity_face_record (user_id);
CREATE INDEX idx_face_rec_supplier ON t_identity_face_record (supplier_id);
CREATE INDEX idx_face_rec_corp ON t_identity_face_record (corporation_id);
CREATE INDEX idx_face_rec_id_card ON t_identity_face_record (id_card);

create table if not exists t_identity_factor_auth_record
(
    id               bigint auto_increment comment '主键ID'
        primary key,
    tenant_id        varchar(20)          not null comment '租户ID',
    record_no        varchar(64)          not null comment '认证记录编号',
    user_id          bigint               null comment '用户ID（可空，兼容非登录场景）',
    supplier_id      bigint               null comment '供应商ID',
    corporation_id   bigint               null comment '作业主体ID',
    name             varchar(100)         not null comment '真实姓名',
    id_card          varchar(18)          not null comment '身份证号',
    bank_card_number varchar(30)          null comment '银行卡号（三要素时必填）',
    phone_number     varchar(20)          null comment '手机号（运营商三要素时必填）',
    auth_type        varchar(30)          not null comment '鉴权类型：TWO_FACTOR,THREE_FACTOR,OPERATOR_THREE_FACTOR',
    auth_scene       varchar(30)          not null comment '认证场景：CONTRACT_SIGNING,PAYMENT_DISBURSEMENT,REAL_NAME_AUTH',
    auth_status      varchar(20)          not null comment '认证状态：SUCCESS,FAILED',
    auth_result      varchar(20)          null comment '鉴权结果：CONSISTENT,INCONSISTENT,NO_RECORD',
    error_code       varchar(50)          null comment '错误码',
    error_message    varchar(500)         null comment '错误信息',
    create_time      datetime             not null comment '创建时间',
    modify_time      datetime             not null comment '修改时间',
    deleted          tinyint(1) default 0 null comment '是否删除',
    deleted_time     datetime             null comment '删除时间',
    version          int        default 0 null comment '版本号'
)
    comment '要素鉴权记录表';

CREATE INDEX idx_factor_auth_user ON t_identity_factor_auth_record (user_id);
CREATE INDEX idx_factor_auth_supplier ON t_identity_factor_auth_record (supplier_id);
CREATE INDEX idx_factor_auth_corp ON t_identity_factor_auth_record (corporation_id);
CREATE INDEX idx_factor_auth_id_card ON t_identity_factor_auth_record (id_card);


create table if not exists t_identity_ocr_record
(
    id                      bigint auto_increment comment '主键ID'
        primary key,
    tenant_id               varchar(20)          not null comment '租户ID',
    record_no               varchar(64)          not null comment '认证记录编号',
    user_id                 bigint               null comment '用户ID（可空，兼容非登录场景）',
    supplier_id             bigint               null comment '供应商ID',
    corporation_id          bigint               null comment '作业主体ID',
    name                    varchar(32)          null comment '真实姓名（OCR识别出的）',
    id_card_no              varchar(20)          null comment '身份证号（OCR识别出的）',
    sex                     varchar(1)           null comment '性别',
    nation                  varchar(10)          null comment '民族',
    birthday                varchar(20)          null comment '出生日期',
    birth_address           varchar(100)         null comment '出生地址',
    id_card_signing_organs  varchar(20)          null comment '身份证签发机关',
    id_card_validity_period varchar(32)          null comment '身份证有效期',
    national_emblem_file_id varchar(32)          null comment '国徽面图片文件ID',
    personal_id_file_id     varchar(32)          null comment '个人证件照面图片文件ID',
    auth_scene              varchar(30)          not null comment '认证场景：CONTRACT_SIGNING,PAYMENT_DISBURSEMENT,REAL_NAME_AUTH',
    ocr_status              varchar(20)          not null comment 'OCR状态：SUCCESS,FAILED',
    confidence_score        decimal(5, 4)        null comment '识别置信度',
    error_code              varchar(50)          null comment '错误码',
    error_message           varchar(500)         null comment '错误信息',
    create_time             datetime             not null comment '创建时间',
    modify_time             datetime             not null comment '修改时间',
    deleted                 tinyint(1) default 0 null comment '是否删除',
    deleted_time            datetime             null comment '删除时间',
    version                 int        default 0 null comment '版本号'
)
    comment 'OCR识别记录表';
CREATE INDEX idx_ocr_rec_user ON t_identity_ocr_record (user_id);
CREATE INDEX idx_ocr_rec_supplier ON t_identity_ocr_record (supplier_id);
CREATE INDEX idx_ocr_rec_corp ON t_identity_ocr_record (corporation_id);
CREATE INDEX idx_ocr_rec_id_card ON t_identity_ocr_record (id_card_no);


create table if not exists t_info_submission_enterprise
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)  null comment '创建时间',
    deleted                 bit          null comment '是否已删除',
    deleted_time            datetime(6)  null comment '删除时间',
    modify_time             datetime(6)  null comment '更新时间',
    tenant_id               varchar(20)  null comment '租户编号',
    version                 int          not null comment '版本',
    supplier_id             bigint       not null comment '灵工平台id',
    supplier_corporation_id bigint       not null comment '作业主体id',
    report_status           varchar(20)  null comment '报送状态',
    name                    varchar(200) not null comment '名称（姓名）',
    social_credit_code      varchar(64)  null comment '统一社会信用代码（纳税人识别号）',
    platform_name           varchar(200) null comment '平台内的平台名称',
    platform_unique_code    varchar(200) null comment '平台内的平台唯一标识码',
    start_date              varchar(20)  null comment '经营开始时间',
    end_date                varchar(20)  null comment '经营结束时间',
    info_status_flag        varchar(20)  null comment '信息状态标识'
)
    comment '企业信息报送表';
CREATE INDEX idx_info_sub_enter_supplier ON t_info_submission_enterprise (supplier_id);
CREATE INDEX idx_info_sub_enter_corp ON t_info_submission_enterprise (supplier_corporation_id);
CREATE INDEX idx_info_sub_status ON t_info_submission_enterprise (supplier_id,report_status);

create table if not exists t_info_submission_labor
(
    id                               bigint auto_increment
        primary key,
    create_time                      datetime(6)  null comment '创建时间',
    deleted                          bit          null comment '是否已删除',
    deleted_time                     datetime(6)  null comment '删除时间',
    modify_time                      datetime(6)  null comment '更新时间',
    tenant_id                        varchar(20)  null comment '租户编号',
    version                          int          not null comment '版本',
    supplier_id                      bigint       not null comment '灵工平台id',
    supplier_corporation_id          bigint       not null comment '作业主体id',
    report_status                    varchar(20)  null comment '报送状态',
    registration_license_obtained    varchar(20)  null comment '是否已取得登记证照',
    name                             varchar(20)  null comment '名称（姓名）',
    unified_social_credit_code       varchar(64)  null comment '统一社会信用代码（纳税人识别号）',
    professional_service_agency_flag varchar(64)  null comment '专业服务机构标识',
    labor_name                       varchar(20)  null comment '姓名',
    certificate_type                 varchar(20)  null comment '证件类型',
    id_card                          varchar(20)  null comment '身份证号',
    household_city                   varchar(50)  null comment '国家或地区',
    income_reporting_exemption_flag  varchar(64)  null comment '是否存在免于报送收入信息情形',
    exemption_type                   varchar(20)  null comment '免报类型',
    household_address                varchar(256) null comment '地址',
    store_name                       varchar(64)  null comment '店铺（用户）名称',
    store_unique_code                varchar(128) null comment '店铺（用户）唯一标识码',
    website_url                      varchar(64)  null comment '网址链接（选填）',
    card_bank                        varchar(64)  null comment '开户银行/非银行支付机构',
    account_name                     varchar(64)  null comment '账户名称',
    bank_card                        varchar(64)  null comment '银行账号/支付账户',
    contact_name                     varchar(20)  null comment '联系人姓名',
    contact_phone                    varchar(20)  null comment '联系电话',
    start_date                       varchar(20)  null comment '经营开始时间',
    end_date                         varchar(20)  null comment '经营结束时间',
    info_status_flag                 varchar(20)  null comment '信息状态标识'
)
    comment '人员信息报送表';
create index idx_info_submission_supplier_id on t_info_submission_labor (supplier_id);
create index idx_info_submission_corporation_id on t_info_submission_labor (supplier_corporation_id);
create index idx_info_submission_id_card on t_info_submission_labor (supplier_corporation_id,id_card);


create table if not exists t_info_submission_labor_income
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6) null comment '创建时间',
    deleted                 bit         null comment '是否已删除',
    deleted_time            datetime(6) null comment '删除时间',
    modify_time             datetime(6) null comment '更新时间',
    tenant_id               varchar(20) null comment '租户编号',
    version                 int         not null comment '版本',
    supplier_id             bigint      not null comment '灵工平台id',
    supplier_corporation_id bigint      not null comment '作业主体id',
    start_date              varchar(20) null comment '开始日期',
    end_date                varchar(20) null comment '结束日期',
    status                  varchar(20) null comment '生成状态',
    file_id                 varchar(64) null comment '附件id'
)
    comment '人员收入信息报送表';
create index idx_supplier_corporation_id
    on t_info_submission_labor_income (supplier_corporation_id);
create index idx_supplier_id
    on t_info_submission_labor_income (supplier_id);


create table if not exists t_invoice
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)    null comment '创建时间',
    deleted                 bit            null comment '是否已删除',
    deleted_time            datetime(6)    null comment '删除时间',
    modify_time             datetime(6)    null comment '更新时间',
    tenant_id               varchar(20)    null comment '租户编号',
    version                 int            not null comment '版本',
    supplier_id             bigint         not null comment '灵工平台id',
    customer_id             bigint         not null comment '客户id',
    supplier_corporation_id bigint         not null comment '作业主体ID',
    contract_id             bigint         not null comment '合同id',
    sn                      varchar(64)    not null comment '申请编号',
    status                  varchar(64)    not null comment '状态',
    type                    varchar(64)    not null comment '发票类型-SPECIAL(专票)/GENERAL(普票)  ',
    fee                     decimal(12, 2) not null comment '开票金额',
    title                   varchar(64)    not null comment '抬头',
    tax_no                  varchar(64)    not null comment '纳税识别号',
    bank_name               varchar(64)    not null comment '开户行',
    bank_account            varchar(64)    not null comment '银行账号',
    register_address        varchar(64)    not null comment '注册地址',
    company_tel             varchar(64)    not null comment '企业电话',
    remark                  varchar(500)   null comment '发票备注',
    apply_remark            varchar(500)   null comment '申请备注',
    addressee_name          varchar(64)    null comment '收件人姓名',
    addressee_mobile        varchar(64)    null comment '收件人电话',
    addressee_address       varchar(64)    null comment '收件人地址',
    addressee_email         varchar(64)    null comment '收件人邮箱',
    invalid_reason          varchar(300)   null comment '作废原因',
    back_reason             varchar(300)   null comment '退回原因',
    invoice_file            varchar(256)   null comment '发票文件'
)
    comment '开票';
CREATE INDEX idx_inv_sup_cust ON t_invoice (supplier_id, customer_id);
CREATE INDEX idx_inv_contract ON t_invoice (contract_id);
CREATE INDEX idx_inv_status ON t_invoice (status);
CREATE unique INDEX idx_inv_sn ON t_invoice (sn);
CREATE INDEX idx_inv_create_time ON t_invoice (create_time);
CREATE INDEX idx_inv_corp ON t_invoice (supplier_corporation_id);
CREATE INDEX idx_inv_supplier ON t_invoice (supplier_id);
CREATE INDEX idx_inv_customer ON t_invoice (customer_id);
CREATE INDEX idx_inv_gen_complex ON t_invoice (supplier_id, customer_id, contract_id, status);

create table if not exists t_invoice_item
(
    id               bigint auto_increment
        primary key,
    create_time      datetime(6)    null comment '创建时间',
    deleted          bit            null comment '是否已删除',
    deleted_time     datetime(6)    null comment '删除时间',
    modify_time      datetime(6)    null comment '更新时间',
    tenant_id        varchar(20)    null comment '租户编号',
    version          int            not null comment '版本',
    contract_id      bigint         not null comment '服务合同id',
    invoice_id       bigint         not null comment '开票id',
    bill_id          bigint         not null comment '账单id',
    invoice_category varchar(64)    not null comment '发票类目',
    fee              decimal(12, 2) not null comment '开票金额'
)
    comment '开票明细';
CREATE INDEX idx_inv_item_invoice ON t_invoice_item (invoice_id);
CREATE INDEX idx_inv_item_bill ON t_invoice_item (bill_id);



create table if not exists t_labor_info
(
    id                          bigint unsigned auto_increment comment 'id'
        primary key,
    labor_id                    bigint           null comment '劳务人员id',
    supplier_id                 bigint           null comment '平台id',
    customer_id                 bigint           null comment '客户id',
    contract_id                 bigint           null comment '服务合同id',
    supplier_corporation_id     bigint           null comment '作业主体id',
    education                   varchar(30)      null comment '最高学历',
    native                      varchar(120)     null comment '籍贯',
    household_registration_type varchar(10)      null comment '户口性质',
    household_city              varchar(50)      null comment '户口所在城市',
    household_address           varchar(256)     null comment '户口所在地址',
    marital_status              varchar(10)      null comment '婚姻状况',
    children                    varchar(20)      null comment '子女状况',
    nation                      varchar(60)      null comment '民族',
    political                   varchar(20)      null comment '政治面貌',
    in_work_day                 date             null comment '参加工作日期',
    dept_id                     bigint           null comment '所在部门id',
    employed                    bigint           null comment '雇佣人员',
    join_date                   date             null comment '加入日期',
    post                        varchar(128)     null comment '岗位',
    emp_status                  varchar(20)      null comment '员工状态',
    work_email                  varchar(128)     null comment '工作邮箱',
    wechat                      varchar(128)     null comment '企业微信',
    personal_wechat             varchar(128)     null comment '个人微信',
    work_mobile                 varchar(20)      null comment '工作电话',
    mobile_number               varchar(20)      null comment '分机号',
    personal_email              varchar(128)     null comment '个人邮箱',
    address                     varchar(128)     null comment '居住地',
    bank_card                   varchar(60)      null comment '工资卡号',
    open_card_city              varchar(64)      null comment '开户城市',
    card_bank                   varchar(64)      null comment '开户行',
    bank_branch                 varchar(64)      null comment '开户支行',
    version                     int              null comment 'version',
    deleted                     bit default b'0' null,
    deleted_time                datetime         null comment '删除时间',
    create_time                 datetime         null comment '创建时间',
    modify_time                 datetime         null comment '更新时间',
    tenant_id                   varchar(32)      null
) comment '合同下劳务人员信息';
CREATE INDEX idx_labor_sup_cust_cont ON t_labor_info (supplier_id, customer_id, contract_id);
CREATE INDEX idx_labor_corp_status ON t_labor_info (supplier_corporation_id, emp_status);
CREATE INDEX idx_labor_labor_id ON t_labor_info (labor_id);
CREATE INDEX idx_labor_supplier ON t_labor_info (supplier_id);
CREATE INDEX idx_labor_customer ON t_labor_info (customer_id);
CREATE INDEX idx_labor_contract ON t_labor_info (contract_id);
CREATE INDEX idx_labor_corp ON t_labor_info (supplier_corporation_id);
CREATE INDEX idx_labor_mgmt_complex ON t_labor_info (supplier_id, customer_id, contract_id, emp_status);

create table if not exists t_labor_protocol
(
    id                      bigint auto_increment comment 'id'
        primary key,
    id_card                 varchar(20) null comment '证件号码',
    name                    varchar(60) null comment '人员姓名',
    supplier_id             bigint      null comment '平台id',
    supplier_corporation_id bigint      null comment '作业主体id',
    tenant_id               varchar(20) null comment '租户',
    protocol_status         varchar(20) null comment '协议状态',
    start_date              date        null comment '协议开始日期',
    end_date                date        null comment '协议截止日期',
    version                 int         null comment 'version',
    deleted                 bit         null,
    deleted_time            datetime    null comment '删除时间',
    create_time             datetime    null,
    modify_time             datetime    null comment '更新时间'
)
    comment '劳务人员协议表';
create index idx_labor_supplier_id on t_labor_protocol (supplier_id);
CREATE INDEX idx_labor_prot_id_corp ON t_labor_protocol (id_card, supplier_corporation_id);
CREATE INDEX idx_labor_prot_status ON t_labor_protocol (protocol_status);
CREATE INDEX idx_labor_prot_id_card ON t_labor_protocol (id_card);
CREATE INDEX idx_labor_prot_corp ON t_labor_protocol (supplier_corporation_id);

create table if not exists t_pay_channel_config
(
    id                 bigint auto_increment comment 'id'
        primary key,
    create_time        datetime(6) null comment '创建时间',
    deleted            bit         null comment '是否已删除',
    deleted_time       datetime(6) null comment '删除时间',
    modify_time        datetime(6) null comment '更新时间',
    tenant_id          varchar(20) null comment '租户编号',
    version            int         not null comment '版本',
    pay_channel        varchar(64) not null comment '通道编码',
    channel_name       varchar(64) not null comment '通道名称',
    pay_channel_config longtext    not null comment '通道配置项(json)'
)
    comment '支付通道配置项';


create table if not exists t_person_info
(
    id           bigint auto_increment
        primary key,
    create_time  datetime(6) null comment '创建时间',
    deleted      bit         null comment '是否已删除',
    deleted_time datetime(6) null comment '删除时间',
    modify_time  datetime(6) null comment '更新时间',
    tenant_id    varchar(20) null comment '租户编号',
    version      int         not null comment '版本',
    cellphone    varchar(11) null comment '手机号',
    name         varchar(20) null comment '人员姓名',
    id_card      varchar(20) null comment '身份证号',
    owner_id     bigint      null comment '实体ID',
    owner_type   varchar(20) null comment '实体类型-SUPPLIER,CUSTOMER,USER,BOSS,TRANS_RECEIPT',
    constraint i_person_info_1
        unique (owner_id, owner_type)
)
    comment '人员信息';

create table if not exists t_personal_income_tax_declare
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6) null comment '创建时间',
    deleted                 bit         null comment '是否已删除',
    deleted_time            datetime(6) null comment '删除时间',
    modify_time             datetime(6) null comment '更新时间',
    tenant_id               varchar(20) null comment '租户编号',
    version                 int         not null comment '版本',
    supplier_id             bigint      not null comment '灵工平台id',
    supplier_corporation_id bigint      not null comment '作业主体id',
    tax_payment_period      varchar(20) null comment '税款所属期',
    income_tax_month        varchar(20) null comment '个税申报月',
    taxpayers_count         varchar(20) null comment '纳税人数',
    current_income          varchar(64) null comment '本期收入',
    status                  varchar(20) null comment '生成状态',
    tax_status              varchar(20) null comment '申报状态'
)
    comment '税务个税申报表';
create index idx_tax_supplier_id on t_personal_income_tax_declare (supplier_id);
create index idx_tax_corp_id on t_personal_income_tax_declare (supplier_corporation_id,tax_payment_period);

create table if not exists t_personal_income_tax_detail
(
    id                                 bigint auto_increment comment '主键'
        primary key,
    create_time                        datetime(6)                 null comment '创建时间',
    deleted                            bit                         null comment '是否已删除',
    deleted_time                       datetime(6)                 null comment '删除时间',
    modify_time                        datetime(6)                 null comment '更新时间',
    tenant_id                          varchar(20)                 null comment '租户编号',
    version                            int                         not null comment '版本',
    supplier_id                        bigint                      not null comment '灵工平台id',
    supplier_corporation_id            bigint                      not null comment '作业主体ID',
    tax_declare_id                     bigint                      not null comment '申报ID',
    name                               varchar(50)                 not null comment '姓名',
    id_card                            varchar(20)                 not null comment '身份证号',
    cellphone                          varchar(20)                 not null comment '手机号',
    current_income                     decimal(22, 2) default 0.00 null comment '本期收入',
    actual_amount                      decimal(22, 2) default 0.00 null comment '本期收入',
    accumulated_income                 decimal(22, 2) default 0.00 null comment '累计收入',
    accumulated_expenses               decimal(22, 2) default 0.00 null comment '累计费用',
    accumulated_tax_free_income        decimal(22, 2) default 0.00 null comment '累计免税收入',
    accumulated_tax_deduction_expenses decimal(22, 2) default 0.00 null comment '累计减除费用',
    accumulated_other_deductions       decimal(22, 2) default 0.00 null comment '累计依法确定的其他扣除',
    accumulated_prepaid_tax            decimal(22, 2) default 0.00 null comment '累计已预缴税额',
    accumulated_tax_reductions         decimal(22, 2) default 0.00 null comment '累计减免税额',
    accumulated_taxable_amount         decimal(22, 2) default 0.00 null comment '累计应纳税额',
    current_withholding_tax            decimal(22, 2) default 0.00 null comment '本期应预扣预缴税额',
    tax_period                         varchar(20)                 not null comment '税款所属期',
    declare_month                      varchar(20)                 not null comment '申报月份'
)
    comment '税务个税申报明细表';
create index idx_per_in_supplier_id
    on t_personal_income_tax_detail (supplier_id);
create index idx_corp_decl on t_personal_income_tax_detail (supplier_corporation_id,id_card, declare_month);


create table if not exists t_previous_income_deduction
(
    id                             bigint unsigned auto_increment comment '主键ID'
        primary key,
    supplier_id                    bigint unsigned             not null comment '服务商ID',
    supplier_corporation_id        bigint unsigned             not null comment '作业主体ID',
    full_name                      varchar(100)                not null comment '姓名',
    id_number                      char(18)                    not null comment '身份证号',
    accumulated_income             decimal(16, 2) default 0.00 null comment '累计收入',
    accumulated_expenses           decimal(16, 2) default 0.00 null comment '累计费用',
    accumulated_deduction_expenses decimal(16, 2) default 0.00 not null comment '累计减除费用 (月数*5000)',
    accumulated_tax_free_income    decimal(16, 2) default 0.00 null comment '累计免税收入',
    accumulated_other_deductions   decimal(16, 2) default 0.00 null comment '累计依法确定的其他扣除',
    accumulated_prepaid_tax        decimal(16, 2) default 0.00 null comment '累计已预缴税额',
    accumulated_tax_reductions     decimal(16, 2) default 0.00 null comment '累计减免税额',
    tax_period                     char(7)                     not null comment '税款所属期（格式：2025-06）',
    upload_time                    datetime                    not null comment '上传时间',
    tenant_id                      varchar(20)                 null comment '租户编号',
    version                        int unsigned   default '0'  not null comment '乐观锁版本号',
    deleted                        bit                         null comment '是否已删除)',
    create_time                    datetime                    null comment '创建时间',
    modify_time                    datetime                    null comment '修改时间',
    deleted_time                   datetime                    null comment '删除时间'
)
    comment '上期收入减除表' charset = utf8mb4;
create index idx_per_income_supplier
    on t_previous_income_deduction (supplier_id);
create index idx_corp_id_per
    on t_previous_income_deduction (supplier_corporation_id,id_number, tax_period);

create table if not exists t_proxy_batch
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)    null comment '创建时间',
    deleted                 bit            null comment '是否已删除',
    deleted_time            datetime(6)    null comment '删除时间',
    modify_time             datetime(6)    null comment '更新时间',
    tenant_id               varchar(20)    null comment '租户编号',
    version                 int            not null comment '版本',
    supplier_id             bigint         not null comment '灵工平台id',
    customer_id             bigint         not null comment '客户id',
    supplier_corporation_id bigint         not null comment '作业主体ID',
    contract_id             bigint         not null comment '合同id',
    salary_statement_id     bigint         not null comment '工资批次id',
    total_amount            decimal(19, 2) null comment '总金额',
    pay_channel             varchar(64)    not null comment '通道编码',
    count                   bigint         null comment '总笔数',
    confirm_time            datetime(6)    null comment '确认出款时间',
    complete_time           datetime(6)    null comment '完成时间',
    last_error_info         varchar(256)   null comment '错误信息',
    batch_status            varchar(20)    not null comment '批次状态 CHECK/PROCESSING/COMPLETE/DELETED',
    remark                  varchar(256)   null comment '备注'
)
    comment '工资代发批次';
CREATE INDEX idx_proxy_bat_sup_corp ON t_proxy_batch (supplier_id, supplier_corporation_id);
CREATE INDEX idx_proxy_bat_status ON t_proxy_batch (batch_status);
CREATE INDEX idx_proxy_bat_supplier ON t_proxy_batch (supplier_id);
CREATE INDEX idx_proxy_bat_corp ON t_proxy_batch (supplier_corporation_id);

create table if not exists t_proxy_order
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)    null comment '创建时间',
    deleted                 bit            null comment '是否已删除',
    deleted_time            datetime(6)    null comment '删除时间',
    modify_time             datetime(6)    null comment '更新时间',
    tenant_id               varchar(20)    null comment '租户编号',
    version                 int            not null comment '版本',
    supplier_id             bigint         not null comment '灵工平台id',
    customer_id             bigint         not null comment '客户id',
    supplier_corporation_id bigint         not null comment '作业主体ID',
    contract_id             bigint         not null comment '合同id',
    proxy_batch_id          bigint         not null comment '代发批次id',
    salary_detail_id        bigint         not null comment '应发工资id',
    name                    varchar(20)    not null comment '姓名',
    id_card                 varchar(20)    not null comment '身份证号',
    cellphone               varchar(11)    null comment '手机号',
    bank_card               varchar(24)    null comment '银行卡号',
    amount                  decimal(19, 2) null comment '金额',
    pay_channel             varchar(64)    not null comment '通道编码',
    complete_time           datetime(6)    null comment '完成时间',
    error_code              varchar(64)    null comment '错误码',
    last_error_info         varchar(256)   null comment '错误信息',
    status                  varchar(20)    not null comment '状态 CHECK_SUCC/CHECK_FAIL/PROCESSING/REMIT/FAIL/DELETED',
    remark                  varchar(256)   null comment '备注'
)
    comment '工资代发订单';
CREATE INDEX idx_proxy_ord_sup_corp ON t_proxy_order (supplier_id, supplier_corporation_id);
CREATE INDEX idx_proxy_ord_batch_stat ON t_proxy_order (proxy_batch_id, status);
CREATE INDEX idx_proxy_ord_sal_dtl ON t_proxy_order (salary_detail_id);
CREATE INDEX idx_proxy_ord_contract ON t_proxy_order (contract_id);
CREATE INDEX idx_proxy_ord_supplier ON t_proxy_order (supplier_id);
CREATE INDEX idx_proxy_ord_corp ON t_proxy_order (supplier_corporation_id);
CREATE INDEX idx_proxy_ord_batch ON t_proxy_order (proxy_batch_id);
CREATE INDEX idx_proxy_ord_status ON t_proxy_order (status);
CREATE INDEX idx_proxy_time_status ON t_proxy_order (create_time,status);

create table if not exists t_role
(
    id           bigint auto_increment
        primary key,
    create_time  datetime(6)      null comment '创建时间',
    deleted      bit              null comment '是否已删除',
    deleted_time datetime(6)      null comment '删除时间',
    modify_time  datetime(6)      null comment '更新时间',
    tenant_id    varchar(20)      null comment '租户编号',
    version      int              not null comment '版本',
    name         varchar(20)      null comment '角色名',
    authorities  longtext         null comment '角色拥有的权限',
    remark       varchar(200)     null comment '备注',
    code         varchar(10)      null comment '角色编码',
    disabled     bit default b'0' not null comment '是否禁用'
) comment '角色表';
create index idx_role_code
    on t_role (code);
create index idx_role_name
    on t_role (name);

create table if not exists t_role_data_scope
(
    id           bigint auto_increment
        primary key,
    create_time  datetime(6) null comment '创建时间',
    deleted      bit         null comment '是否已删除',
    deleted_time datetime(6) null comment '删除时间',
    modify_time  datetime(6) null comment '更新时间',
    tenant_id    varchar(20) null comment '租户编号',
    version      int         not null comment '版本',
    name         varchar(20) null comment '角色名',
    code         varchar(10) null comment '角色编码',
    role_id      bigint      not null comment '角色id',
    data_type    varchar(32) null comment '权限类型:CORPORATION-作业主体,CUSTOMER-客户,CONTRACT-合同',
    data_id      bigint      null comment '权限类型对应数据ID'
) comment '角色数据权限';
create index idx_role_id on t_role_data_scope (role_id);
create index idx_data_id on t_role_data_scope (data_type,data_id);

create table if not exists t_role_member
(
    id           bigint auto_increment
        primary key,
    create_time  datetime(6) null comment '创建时间',
    deleted      bit         null comment '是否已删除',
    deleted_time datetime(6) null comment '删除时间',
    modify_time  datetime(6) null comment '更新时间',
    tenant_id    varchar(20) null comment '租户编号',
    version      int         not null comment '版本',
    role_id      bigint      not null comment '角色ID',
    subject_id   bigint      null comment '用户ID'
) comment '角色用户关联表';
create index idx_role_id on t_role_member (role_id);
create index idx_subject_id on t_role_member (subject_id);

create table if not exists t_salary_detail
(
    id                             bigint auto_increment comment '员工工资明细ID'
        primary key,
    salary_statement_id            bigint                      not null comment '工资表ID',
    name                           varchar(100)                not null comment '姓名',
    id_card                        varchar(18)                 not null comment '身份证号',
    phone_number                   varchar(20)                 not null comment '手机号',
    tax_period                     varchar(7)                  not null comment '税款所属期 (YYYY-MM)',
    supplier_corporation_id        bigint                      not null comment '作业主体ID',
    payable_amount                 decimal(16, 2) default 0.00 not null comment '本次应发金额',
    tax_free_income                decimal(16, 2) default 0.00 not null comment '本次免税收入',
    other_deductions               decimal(16, 2) default 0.00 not null comment '本次依法确定的其他扣除',
    tax_relief_amount              decimal(16, 2) default 0.00 not null comment '本次减免税额',
    accumulated_income             decimal(16, 2) default 0.00 not null comment '累计收入',
    accumulated_deduction_expenses decimal(16, 2) default 0.00 not null comment '累计减除费用 (月数*5000)',
    accumulated_expenses           decimal(16, 2) default 0.00 not null comment '累计费用（累计收入*20%）',
    accumulated_tax_free_income    decimal(16, 2) default 0.00 not null comment '累计免税收入 (累加)',
    accumulated_other_deductions   decimal(16, 2) default 0.00 not null comment '累计依法确定的其他扣除 (累加)',
    accumulated_tax_relief         decimal(16, 2) default 0.00 not null comment '累计减免税额 (累加)',
    accumulated_taxable_amount     decimal(16, 2) default 0.00 not null comment '累计应纳税所得额',
    accumulated_tax_amount         decimal(16, 2) default 0.00 not null comment '累计应纳税额',
    accumulated_prepaid_tax        decimal(16, 2) default 0.00 not null comment '累计已预缴税额',
    current_tax_amount             decimal(16, 2) default 0.00 not null comment '本期应纳税额',
    current_withholding_tax        decimal(16, 2) default 0.00 not null comment '本次应预扣预缴税额（实际扣款）',
    net_payment                    decimal(16, 2) default 0.00 not null comment '实发金额',
    vat_amount                     decimal(16, 2) default 0.00 not null comment '增值税额',
    additional_tax_amount          decimal(16, 2) default 0.00 not null comment '增值附加税额',
    urban_construction_tax         decimal(16, 2) default 0.00 not null comment '城市维护建设附加税',
    education_surcharge            decimal(16, 2) default 0.00 not null comment '教育费附加税',
    local_education_surcharge      decimal(16, 2) default 0.00 not null comment '地方教育附加税',
    tenant_id                      varchar(20)                 null comment '租户编号',
    version                        int unsigned   default '0'  not null comment '乐观锁版本号',
    deleted                        bit                         null comment '是否已删除',
    create_time                    datetime(6)                 null comment '创建时间',
    modify_time                    datetime(6)                 null comment '修改时间',
    deleted_time                   datetime(6)                 null comment '删除时间'
)
    comment '工资明细表';
create index idx_corporation_period
    on t_salary_detail (supplier_corporation_id, tax_period);
create index idx_create_time
    on t_salary_detail (create_time);
create index idx_id_card_corporation_period
    on t_salary_detail (id_card, supplier_corporation_id, tax_period);
create index idx_salary_statement_id
    on t_salary_detail (salary_statement_id);
create index idx_tenant_deleted
    on t_salary_detail (tenant_id, deleted);
CREATE INDEX idx_sal_dtl_period ON t_salary_detail (tax_period);
CREATE INDEX idx_sal_calc_complex ON t_salary_detail (supplier_corporation_id, id_card, tax_period);





create table if not exists t_salary_statement
(
    id                      bigint unsigned auto_increment comment '工资表ID'
        primary key,
    customer_id             bigint unsigned             not null comment '客户ID',
    contract_id             bigint unsigned             not null comment '服务合同ID',
    supplier_id             bigint unsigned             not null comment '服务商ID',
    supplier_corporation_id bigint unsigned             not null comment '作业主体ID',
    tax_period              char(7)                     not null comment '税款所属期（格式：2025-06）',
    total_people            int unsigned   default '0'  null comment '总人数',
    total_payable           decimal(16, 2) default 0.00 null comment '应发金额总计',
    total_income_tax        decimal(16, 2) default 0.00 null comment '个税预缴额总计',
    total_vat               decimal(16, 2) default 0.00 null comment '增值税应纳税额总计',
    total_surtax            decimal(16, 2) default 0.00 null comment '附加税应纳税额总计',
    net_payment_total       decimal(16, 2) default 0.00 null comment '实发金额总计',
    tax_declaration_month   char(7)                     not null comment '个税申报月（格式：2025-07）',
    status                  char(30)                    null comment '状态（CALCULATING=算税中,UNCONFIRMED=待确认,CONFIRMED=已确认）',
    upload_time             datetime(6)                 not null comment '上传时间',
    tenant_id               varchar(20)                 null comment '租户编号',
    version                 int unsigned   default '0'  not null comment '乐观锁版本号',
    deleted                 bit                         null comment '是否已删除',
    create_time             datetime(6)                 null comment '创建时间',
    modify_time             datetime(6)                 null comment '修改时间',
    deleted_time            datetime(6)                 null comment '删除时间'
)
    comment '工资表';

CREATE INDEX idx_sal_stmt_sup_cont ON t_salary_statement (supplier_id, contract_id);
CREATE INDEX idx_sal_stmt_cust_corp ON t_salary_statement (customer_id, supplier_corporation_id);
CREATE INDEX idx_sal_stmt_status ON t_salary_statement (status);
CREATE INDEX idx_sal_stmt_contract ON t_salary_statement (contract_id);
CREATE INDEX idx_sal_stmt_supplier ON t_salary_statement (supplier_id);
CREATE INDEX idx_sal_stmt_customer ON t_salary_statement (customer_id);
CREATE INDEX idx_sal_stmt_corp ON t_salary_statement (supplier_corporation_id);
CREATE INDEX idx_sal_stmt_create_time ON t_salary_statement (create_time);






create table if not exists t_sequence
(
    id           varchar(400) not null
        primary key,
    create_time  datetime     null comment '创建时间',
    modify_time  datetime     null comment '更新时间',
    version      int          not null comment '版本',
    tenant_id    varchar(20)  null comment '租户编号',
    deleted      bit          null comment '是否已删除',
    deleted_time datetime     null comment '删除时间',
    next_value   bigint       not null
)
    comment '序列表';

create table if not exists t_supplier
(
    id                   bigint auto_increment
        primary key,
    create_time          datetime(6)  null comment '创建时间',
    deleted              bit          null comment '是否已删除',
    deleted_time         datetime(6)  null comment '删除时间',
    modify_time          datetime(6)  null comment '更新时间',
    tenant_id            varchar(20)  null comment '租户编号',
    version              int          not null comment '版本',
    supplier_no          varchar(100) null comment '服务商编号',
    name                 varchar(20)  null comment '名称',
    enterprise_info_id   bigint       null comment '企业信息ID',
    business_create_time datetime(6)  null comment '业务创建时间',
    signature_code       varchar(50)  null comment '短信签名',
    disabled             bit          null comment '是否禁用(0:否,1:禁用)',
    admin_user_id        bigint       null comment '管理员用户ID'
)
    comment '灵工平台服务商信息';
CREATE INDEX idx_sup_name ON t_supplier (name);
CREATE INDEX idx_sup_ctime ON t_supplier (create_time);

create table if not exists t_supplier_corporation
(
    id                 bigint auto_increment comment 'id'
        primary key,
    create_time        datetime(6) null comment '创建时间',
    deleted            bit         null comment '是否已删除',
    deleted_time       datetime(6) null comment '删除时间',
    modify_time        datetime(6) null comment '更新时间',
    tenant_id          varchar(20) null comment '租户编号',
    version            int         not null comment '版本',
    supplier_id        bigint      not null comment '灵工平台id',
    enterprise_info_id bigint      null comment '企业信息ID',
    name               varchar(64) not null comment '公司名称',
    social_credit_code varchar(64) not null comment '统一社会信用代码',
    bank_name          varchar(64) null comment '开户行',
    bank_account       varchar(64) null comment '开户账号',
    company_tel        varchar(64) null comment '企业电话',
    disabled           bit         null comment '是否禁用',
    tax_uuid           varchar(64) null comment '税局给的平台UUID'
)
    comment '作业主体信息';
CREATE INDEX idx_sup_corp_supplier ON t_supplier_corporation (supplier_id);
CREATE INDEX idx_sup_corp_name ON t_supplier_corporation (name);
CREATE INDEX idx_sup_corp_ctime ON t_supplier_corporation (create_time);

create table if not exists t_supplier_domain_config
(
    id                   bigint auto_increment
        primary key,
    create_time          datetime(6)  null comment '创建时间',
    deleted              bit          null comment '是否已删除',
    deleted_time         datetime(6)  null comment '删除时间',
    modify_time          datetime(6)  null comment '更新时间',
    tenant_id            varchar(20)  null comment '租户编号',
    version              int          not null comment '版本',
    supplier_id          bigint       not null comment '服务运营方编号',
    domain_name          varchar(60)  null comment '二级域名',
    slogan               varchar(20)  null comment 'slogan',
    logo_url             varchar(255) null comment 'logo地址',
    brand_name           varchar(255) null comment '品牌名称',
    h5_domain_name       varchar(60)  null comment 'h5域名',
    h5_logo_url          varchar(255) null comment 'h5_logo地址',
    h5_service_agreement text         null comment 'h5服务协议文件及文件地址,以json方式存储'
)
    comment '品牌信息配置表';
create index idx_domain_name on t_supplier_domain_config (domain_name);
create index idx_supplier_id on t_supplier_domain_config (supplier_id);
create index idx_h5_domain on t_supplier_domain_config (h5_domain_name);

create table if not exists t_supplier_labor
(
    id             bigint auto_increment
        primary key,
    create_time    datetime(6)              null comment '创建时间',
    deleted        bit         default b'0' null comment '是否已删除',
    deleted_time   datetime(6)              null comment '删除时间',
    modify_time    datetime(6)              null comment '更新时间',
    tenant_id      varchar(20)              null comment '租户编号',
    version        int                      not null comment '版本',
    supplier_id    bigint                   not null comment '服务运营方编号',
    name           varchar(20)              null comment '姓名',
    id_card        varchar(20)              null comment '身份证号',
    cellphone      varchar(11) default '0'  not null comment '手机号,平台下唯一',
    birthday_date  varchar(40)              null comment '出生日期',
    id_card_period varchar(40)              null comment '证件有效期',
    auth_status    char        default '0'  null comment '实名认证状态',
    auth_time      datetime                 null comment '实名认证时间'
)
    comment '平台劳务人员表';
create index idx_supplier_id on t_supplier_labor (supplier_id);
CREATE INDEX idx_sup_labor_name_phone ON t_supplier_labor (name, cellphone);
CREATE INDEX idx_sup_labor_su_card ON t_supplier_labor (supplier_id,id_card);
CREATE INDEX idx_sup_labor_id_card ON t_supplier_labor (id_card);
CREATE INDEX idx_sup_labor_cellphone ON t_supplier_labor (cellphone);

create table if not exists t_supplier_member
(
    id             bigint auto_increment
        primary key,
    create_time    datetime(6) null comment '创建时间',
    deleted        bit         null comment '是否已删除',
    deleted_time   datetime(6) null comment '删除时间',
    modify_time    datetime(6) null comment '更新时间',
    tenant_id      varchar(20) null comment '租户编号',
    version        int         not null comment '版本',
    supplier_id    bigint      not null comment '服务运营方编号',
    last_used_time datetime(6) null comment '上一次使用的时间',
    disabled       bit         null comment '是否禁用',
    user_id        bigint      null comment '用户ID',
    owner_type     varchar(20) null comment '所属类型:1-平台用户,2-客户用户',
    owner_Id       bigint      not null comment '平台id,客户id',
    name           varchar(20) null comment '姓名',
    cellphone      varchar(11) not null comment '手机号'
)
    comment '平台用户表';
create index idx_owner_id
    on t_supplier_member (owner_type,owner_Id);


create table if not exists t_supplier_pay_channel
(
    id             bigint auto_increment comment 'id'
        primary key,
    create_time    datetime(6) null comment '创建时间',
    deleted        bit         null comment '是否已删除',
    deleted_time   datetime(6) null comment '删除时间',
    modify_time    datetime(6) null comment '更新时间',
    tenant_id      varchar(20) null comment '租户编号',
    version        int         not null comment '版本',
    supplier_id    bigint      not null comment '灵工平台id',
    pay_channel    varchar(64) not null comment '通道编码',
    pay_channel_id bigint      null
)
    comment '平台可用支付通道配置';
CREATE unique INDEX idx_sup_pay_channel_su_pay_channel ON t_supplier_pay_channel (supplier_id, pay_channel);

create table if not exists t_supplier_sms_template
(
    id            bigint auto_increment comment 'id'
        primary key,
    create_time   datetime(6)  null comment '创建时间',
    deleted       bit          null comment '是否已删除',
    deleted_time  datetime(6)  null comment '删除时间',
    modify_time   datetime(6)  null comment '更新时间',
    tenant_id     varchar(20)  null comment '租户编号',
    version       int          not null comment '版本',
    supplier_id   bigint       not null comment '灵工平台id',
    business_type varchar(64)  null comment '短信业务类型',
    template_code varchar(256) null comment '短信模板编码',
    constraint udx_supplier_bus
        unique (supplier_id, business_type)
)
    comment '平台短信配置';
create index idx_supplier_id on t_supplier_sms_template (supplier_id,business_type);

create table if not exists t_task
(
    id           bigint auto_increment comment '任务ID'
        primary key,
    task_type    varchar(25)   null comment '任务类型',
    task_status  varchar(25)   null comment '任务状态',
    task_param   varchar(1024) null comment '任务参数',
    task_result  varchar(255)  null comment '任务执行结果，可以是文本或JSON格式',
    finish_time  datetime(6)   null comment '任务结束时间',
    file_name    varchar(255)  null comment '文件名称',
    attachments  longtext      null comment '附件文件ID列表',
    owner_type   varchar(25)   null comment '实体类型',
    owner_id     bigint        null comment '实体id',
    create_time  datetime(6)   null comment '任务创建时间',
    deleted      bit           null comment '是否已删除',
    deleted_time datetime(6)   null comment '删除时间',
    modify_time  datetime(6)   null comment '更新时间',
    tenant_id    varchar(20)   null comment '租户编号',
    version      int           null comment '版本'
)
    comment '异步任务表，用于存储任务的核心信息';


create table if not exists t_tax_payment_voucher
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6)   null comment '创建时间',
    deleted                 bit           null comment '是否已删除',
    deleted_time            datetime(6)   null comment '删除时间',
    modify_time             datetime(6)   null comment '更新时间',
    tenant_id               varchar(20)   null comment '租户编号',
    version                 int           not null comment '版本',
    supplier_id             bigint        not null comment '灵工平台id',
    supplier_corporation_id bigint        not null comment '作业主体id',
    tax_payment_period      varchar(20)   null comment '税款所属期',
    file_ids                varchar(5000) null comment '附件id,逗号分隔'
)
    comment '税务税款缴纳表';
create index idx_supplier_id
    on t_tax_payment_voucher (supplier_id);
create index idx_corp_period
    on t_tax_payment_voucher (supplier_corporation_id, tax_payment_period);

create table if not exists t_user
(
    id           bigint auto_increment
        primary key,
    create_time  datetime(6) null comment '创建时间',
    deleted      bit         null comment '是否已删除',
    deleted_time datetime(6) null comment '删除时间',
    modify_time  datetime(6) null comment '更新时间',
    tenant_id    varchar(20) null comment '租户编号',
    version      int         not null comment '版本',
    name         varchar(20) null comment '用户名',
    account_no   varchar(20) null comment '账号',
    password     varchar(50) null comment '密码',
    cellphone    varchar(11) null comment '手机号',
    constraint idx_tenant_cell
        unique (tenant_id, cellphone),
    constraint t_user_tenant_acc
        unique (tenant_id, account_no)
)
    comment '用户';
create index idx_tenant_id on t_user (tenant_id,cellphone);

create table if not exists t_value_added_tax_declare
(
    id                      bigint auto_increment
        primary key,
    create_time             datetime(6) null comment '创建时间',
    deleted                 bit         null comment '是否已删除',
    deleted_time            datetime(6) null comment '删除时间',
    modify_time             datetime(6) null comment '更新时间',
    tenant_id               varchar(20) null comment '租户编号',
    version                 int         not null comment '版本',
    supplier_id             bigint      not null comment '灵工平台id',
    supplier_corporation_id bigint      not null comment '作业主体id',
    tax_payment_period      varchar(20) null comment '税款所属期',
    income_tax_month        varchar(20) null comment '个税申报月',
    taxpayers_count         varchar(20) null comment '纳税人数',
    current_income          varchar(64) null comment '本期收入',
    status                  varchar(20) null comment '生成状态',
    tax_status              varchar(20) null comment '申报状态'
)
    comment '税务增值税申报表';
create index idx_supp_id on t_value_added_tax_declare (supplier_id);
create index idx_tax_corp_id on t_value_added_tax_declare (supplier_corporation_id,tax_payment_period);

create table if not exists t_value_added_tax_detail
(
    id                         bigint auto_increment comment '主键'
        primary key,
    create_time                datetime(6)                 null comment '创建时间',
    deleted                    bit                         null comment '是否已删除',
    deleted_time               datetime(6)                 null comment '删除时间',
    modify_time                datetime(6)                 null comment '更新时间',
    tenant_id                  varchar(20)                 null comment '租户编号',
    version                    int                         not null comment '版本',
    supplier_id                bigint                      not null comment '灵工平台id',
    supplier_corporation_id    bigint                      not null comment '作业主体ID',
    value_added_tax_id         bigint                      not null comment '增值税申报ID',
    tax_period                 varchar(20)                 not null comment '税款所属期',
    declare_month              varchar(20)                 not null comment '申报月份',
    name                       varchar(50)                 null comment '姓名',
    certificate_type           varchar(20)                 null comment '证件类型',
    id_card                    varchar(20)                 null comment '证件号码',
    country_region             varchar(50)                 null comment '国家或地区',
    address                    varchar(200)                null comment '地址',
    user_name                  varchar(50)                 null comment '用户名称',
    user_unique_code           varchar(64)                 null comment '用户唯一标识码',
    tax_basis                  decimal(22, 2) default 0.00 null comment '计税依据 (增值税)',
    tax_item                   varchar(50)                 null comment '征收品目 (增值税)',
    tax_rate                   varchar(20)                 null comment '征收率 (增值税)',
    vat_amount                 decimal(22, 2) default 0.00 null comment '本期应纳税额 (增值税)',
    vat_exemption_code         varchar(20)                 null comment '减免性质代码 (增值税)',
    vat_exemption_amount       decimal(22, 2) default 0.00 null comment '减免税额 (增值税)',
    vat_payable                decimal(22, 2) default 0.00 null comment '应补(退)税额 (增值税)',
    urban_tax_rate             varchar(20)                 null comment '适用税率 (城市维护建设税)',
    urban_tax_amount           decimal(22, 2) default 0.00 null comment '本期应纳税额 (城市维护建设税)',
    urban_exemption_code       varchar(20)                 null comment '减免性质代码 (城市维护建设税)',
    urban_exemption_amount     decimal(22, 2) default 0.00 null comment '减免税额 (城市维护建设税)',
    urban_tax_payable          decimal(22, 2) default 0.00 null comment '应补(退)税额 (城市维护建设税)',
    edu_tax_rate               varchar(20)                 null comment '适用税率 (教育附加)',
    edu_tax_amount             decimal(22, 2) default 0.00 null comment '本期应纳税额 (教育附加)',
    edu_exemption_code         varchar(20)                 null comment '减免性质代码 (教育附加)',
    edu_exemption_amount       decimal(22, 2) default 0.00 null comment '减免税额 (教育附加)',
    edu_tax_payable            decimal(22, 2) default 0.00 null comment '应补(退)税额 (教育附加)',
    local_edu_tax_rate         varchar(20)                 null comment '适用税率 (地方教育附加)',
    local_edu_tax_amount       decimal(22, 2) default 0.00 null comment '本期应纳税额 (地方教育附加)',
    local_edu_exemption_code   varchar(20)                 null comment '减免性质代码 (地方教育附加)',
    local_edu_exemption_amount decimal(22, 2) default 0.00 null comment '减免税额 (地方教育附加)',
    local_edu_tax_payable      decimal(22, 2) default 0.00 null comment '应补(退)税额 (地方教育附加)'
)
    comment '税务增值税申报明细表';
create index idx_atax_id on t_value_added_tax_detail (value_added_tax_id);
create index idx_supp_id on t_value_added_tax_detail (supplier_id);
create index idx_corp_id on t_value_added_tax_detail (supplier_corporation_id,id_card,tax_period);

create table if not exists olading_labor.t_archive
(
    id              bigint auto_increment comment '主键ID'
        primary key,
    name            varchar(50)                              null comment '文件名',
    compressed_data longblob                                 null comment '已压缩的原始数据,使用GZ压缩',
    data_size       bigint                                   null comment '数据大小',
    sha256          varchar(64)                              null comment 'sha256',
    tenant_id       varchar(20)                              null comment '租户编号',
    create_time     datetime(6) default CURRENT_TIMESTAMP(6) null comment '创建时间',
    modify_time     datetime(6) default CURRENT_TIMESTAMP(6) null on update CURRENT_TIMESTAMP(6) comment '修改时间',
    deleted         bit         default b'0'                 null comment '是否删除',
    deleted_time    datetime(6)                              null comment '删除时间',
    version         int         default 0                    not null comment '版本号'
)
    comment '存储小文件表';

create index idx_archive_name
    on olading_labor.t_archive (name);

create index idx_archive_sha256
    on olading_labor.t_archive (sha256);




create table if not exists t_notification
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    receiver       varchar(50)                              not null comment '通知接收人',
    url_hash       varchar(50)                              not null comment '通知url的hash',
    content_format varchar(20)                              not null comment '通知内容格式',
    content        longtext                                 null comment '通知内容',
    request_count  int         default 0                    not null comment '请求次数',
    ok             bit         default b'0'                 not null comment '是否成功',
    tenant_id      varchar(20)                              null comment '租户编号',
    create_time    datetime(6) default CURRENT_TIMESTAMP(6) null comment '创建时间',
    modify_time    datetime(6) default CURRENT_TIMESTAMP(6) null on update CURRENT_TIMESTAMP(6) comment '修改时间',
    deleted        bit         default b'0'                 null comment '是否删除',
    deleted_time   datetime(6)                              null comment '删除时间',
    version        int         default 0                    not null comment '版本号'
)
    comment 'API推送通知表';

create index idx_notification_ok
    on olading_labor.t_notification (ok);

create index idx_notification_receiver
    on olading_labor.t_notification (receiver);

create index idx_notification_url_hash
    on olading_labor.t_notification (url_hash);



create table if not exists t_notification_url_hash
(
    id           bigint auto_increment comment '主键ID'
        primary key,
    url          varchar(255)                             not null comment '通知URL',
    url_hash     varchar(50)                              not null comment '通知url的hash',
    tenant_id    varchar(20)                              null comment '租户编号',
    create_time  datetime(6) default CURRENT_TIMESTAMP(6) null comment '创建时间',
    modify_time  datetime(6) default CURRENT_TIMESTAMP(6) null on update CURRENT_TIMESTAMP(6) comment '修改时间',
    deleted      bit         default b'0'                 null comment '是否删除',
    deleted_time datetime(6)                              null comment '删除时间',
    version      int         default 0                    not null comment '版本号'
)
    comment '推送URL哈希表';






























