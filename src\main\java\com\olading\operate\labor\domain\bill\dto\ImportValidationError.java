package com.olading.operate.labor.domain.bill.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入验证错误信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportValidationError {
    
    /**
     * 行号
     */
    private int rowNum;
    
    /**
     * 字段名
     */
    private String fieldName;
    
    /**
     * 错误信息
     */
    private String errorMessage;
}